#!/usr/bin/env python3
"""
Simple Trading Bot - Direct implementation without complex imports
"""

import os
import time
import json
import logging
from datetime import datetime
import pandas as pd
import numpy as np

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

class SimpleMT4Broker:
    """Simple MT4 file-based broker"""
    
    def __init__(self, config):
        self.config = config
        self.connected = False
        
        # Find MT4 data path
        mt4_paths = [
            os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal"),
            os.path.join(os.getcwd(), "mt4_files")
        ]
        
        self.mt4_data_path = None
        for path in mt4_paths:
            if os.path.exists(path):
                if "MetaQuotes" in path:
                    for folder in os.listdir(path):
                        terminal_path = os.path.join(path, folder, "MQL4", "Files")
                        if os.path.exists(terminal_path):
                            self.mt4_data_path = terminal_path
                            break
                else:
                    self.mt4_data_path = path
                break
        
        if not self.mt4_data_path:
            self.mt4_data_path = os.path.join(os.getcwd(), "mt4_files")
            os.makedirs(self.mt4_data_path, exist_ok=True)
        
        self.status_file = os.path.join(self.mt4_data_path, "mt4_status.txt")
        self.commands_file = os.path.join(self.mt4_data_path, "python_commands.txt")
        self.responses_file = os.path.join(self.mt4_data_path, "python_responses.txt")
        
        self.connect()
    
    def connect(self):
        """Check MT4 connection"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r') as f:
                    status = f.read()
                if "MT4_READY" in status:
                    # Test actual communication
                    test_response = self.send_command({"command": "ping"})
                    if test_response and test_response.get('success'):
                        self.connected = True
                        logger.info("✓ Connected to MT4 via file communication - REAL TRADING MODE")
                        return True
                    else:
                        logger.warning("⚠ MT4 status file exists but communication failed")

            logger.warning("⚠ MT4 not ready - running in simulation mode")
            return False
        except Exception as e:
            logger.error(f"Connection error: {e}")
            return False
    
    def is_connected(self):
        return self.connected
    
    def send_command(self, command):
        """Send command to MT4"""
        try:
            if not self.connected:
                return None
            
            with open(self.commands_file, 'w') as f:
                json.dump(command, f)
            
            # Wait for response
            for i in range(10):
                if os.path.exists(self.responses_file):
                    time.sleep(0.1)
                    try:
                        with open(self.responses_file, 'r') as f:
                            response = json.load(f)
                        os.remove(self.responses_file)
                        return response
                    except:
                        pass
                time.sleep(0.5)
            
            return None
        except Exception as e:
            logger.error(f"Command error: {e}")
            return None
    
    def get_account_info(self):
        """Get account info"""
        if self.connected:
            response = self.send_command({"command": "account_info"})
            if response and response.get('success'):
                return response['data']
        
        # Simulation fallback
        return {
            'login': self.config['login'],
            'balance': 10000.0,
            'equity': 10000.0,
            'currency': 'USD',
            'server': self.config['server']
        }
    
    def get_price(self, symbol):
        """Get current price"""
        if self.connected:
            response = self.send_command({"command": "get_price", "symbol": symbol})
            if response and response.get('success'):
                return response['data']
        
        # Simulation prices
        mock_prices = {
            'XAUUSD': {'bid': 2000.50, 'ask': 2000.70},
            'EURUSD': {'bid': 1.0850, 'ask': 1.0852},
            'GBPUSD': {'bid': 1.2650, 'ask': 1.2652},
            'NAS100': {'bid': 15000.0, 'ask': 15001.0},
            'US30': {'bid': 35000.0, 'ask': 35001.0}
        }
        return mock_prices.get(symbol, {'bid': 1.0000, 'ask': 1.0001})
    
    def place_order(self, symbol, order_type, volume, comment=""):
        """Place order"""
        if self.connected:
            cmd = {
                "command": "place_order",
                "symbol": symbol,
                "order_type": order_type,
                "volume": volume,
                "comment": comment
            }
            response = self.send_command(cmd)
            if response and response.get('success'):
                logger.info(f"✓ REAL ORDER PLACED: {symbol} {order_type} {volume} lots - Ticket: {response['data'].get('ticket', 'N/A')}")
                return response['data']
            else:
                logger.error(f"✗ Order failed: {response}")
                return None

        # Simulation fallback
        logger.info(f"SIMULATION: {order_type} order for {symbol}, volume: {volume}")
        return {'ticket': int(time.time()), 'success': True}

class SimpleAnalyzer:
    """Simple market analyzer"""
    
    def analyze_symbol(self, symbol, broker):
        """Analyze a symbol for trading signals"""
        try:
            # Get current price
            price_data = broker.get_price(symbol)
            current_price = price_data['bid']
            
            # Generate mock historical data for SMA calculation
            prices = []
            base_price = current_price
            for i in range(50):
                # Create some price variation
                variation = np.random.uniform(-0.01, 0.01)
                price = base_price * (1 + variation)
                prices.append(price)
                base_price = price
            
            prices.reverse()  # Most recent last
            prices = np.array(prices)
            
            # Calculate SMAs
            sma_10 = np.mean(prices[-10:])
            sma_20 = np.mean(prices[-20:])
            prev_sma_10 = np.mean(prices[-11:-1])
            prev_sma_20 = np.mean(prices[-21:-1])
            
            # Determine signal
            signal = 'HOLD'
            confidence = 0.5
            
            # Golden cross (SMA10 crosses above SMA20)
            if prev_sma_10 <= prev_sma_20 and sma_10 > sma_20:
                signal = 'BUY'
                confidence = 0.8
            # Death cross (SMA10 crosses below SMA20)
            elif prev_sma_10 >= prev_sma_20 and sma_10 < sma_20:
                signal = 'SELL'
                confidence = 0.8
            # Strong trend
            elif sma_10 > sma_20 * 1.005:  # 0.5% above
                signal = 'BUY'
                confidence = 0.6
            elif sma_10 < sma_20 * 0.995:  # 0.5% below
                signal = 'SELL'
                confidence = 0.6
            
            return {
                'symbol': symbol,
                'signal': signal,
                'confidence': confidence,
                'sma_10': sma_10,
                'sma_20': sma_20,
                'current_price': current_price
            }
            
        except Exception as e:
            logger.error(f"Analysis error for {symbol}: {e}")
            return {'symbol': symbol, 'signal': 'HOLD', 'confidence': 0.0}

class SimpleTradingBot:
    """Simple trading bot"""
    
    def __init__(self):
        # Configuration
        self.config = {
            'server': 'FBS-Demo',
            'login': 11653999,
            'password': 'kTYg$53Z'
        }
        
        self.instruments = ['XAUUSD', 'EURUSD', 'GBPUSD', 'NAS100', 'US30']
        self.scan_interval = 60  # seconds
        self.max_positions = 3
        self.position_size = 0.1  # lots
        
        # Initialize components
        self.broker = SimpleMT4Broker(self.config)
        self.analyzer = SimpleAnalyzer()
        self.open_positions = {}
        
        logger.info("✓ Trading bot initialized")
    
    def run(self):
        """Main trading loop"""
        logger.info("🚀 Starting trading bot...")
        logger.info("Press Ctrl+C to stop")
        
        try:
            while True:
                self.scan_markets()
                time.sleep(self.scan_interval)
                
        except KeyboardInterrupt:
            logger.info("Trading bot stopped by user")
        except Exception as e:
            logger.error(f"Bot error: {e}")
    
    def scan_markets(self):
        """Scan all instruments for trading opportunities"""
        logger.info("📊 Scanning markets...")
        
        # Get account info
        account = self.broker.get_account_info()
        logger.info(f"Account {account['login']}: Balance=${account['balance']:.2f}")
        
        # Analyze each instrument
        for symbol in self.instruments:
            try:
                analysis = self.analyzer.analyze_symbol(symbol, self.broker)
                
                logger.info(f"{symbol}: {analysis['signal']} "
                          f"(confidence: {analysis['confidence']:.1f}, "
                          f"price: {analysis['current_price']:.5f})")
                
                # Check for trading opportunity
                if (analysis['signal'] in ['BUY', 'SELL'] and 
                    analysis['confidence'] > 0.7 and 
                    len(self.open_positions) < self.max_positions and
                    symbol not in self.open_positions):
                    
                    self.place_trade(symbol, analysis)
                    
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
    
    def place_trade(self, symbol, analysis):
        """Place a trade based on analysis"""
        try:
            order_type = analysis['signal']
            
            result = self.broker.place_order(
                symbol=symbol,
                order_type=order_type,
                volume=self.position_size,
                comment=f"Auto trade: SMA crossover"
            )
            
            if result and result.get('success'):
                self.open_positions[symbol] = {
                    'ticket': result['ticket'],
                    'type': order_type,
                    'volume': self.position_size,
                    'entry_time': datetime.now(),
                    'entry_price': analysis['current_price']
                }
                
                logger.info(f"🎯 Trade placed: {symbol} {order_type} {self.position_size} lots")
            
        except Exception as e:
            logger.error(f"Error placing trade for {symbol}: {e}")

def main():
    """Main function"""
    print("=" * 60)
    print("🤖 SIMPLE TRADING BOT")
    print("=" * 60)
    
    try:
        bot = SimpleTradingBot()
        bot.run()
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")

if __name__ == "__main__":
    main()
