//+------------------------------------------------------------------+
//|                                           PythonConnector.mq4   |
//|                        Copyright 2025, Ultimate Trading Bot     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Ultimate Trading Bot v3.0"
#property link      ""
#property version   "3.00"
#property strict

// File paths for communication
string commands_file = "python_commands.txt";
string responses_file = "python_responses.txt";
string status_file = "mt4_status.txt";

// Global variables
datetime last_check = 0;
int check_interval = 100; // Check every 100ms

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("Ultimate Trading Bot - Python Connector v3.0 Started");
    
    // Create status file to indicate MT4 is ready
    int file_handle = FileOpen(status_file, FILE_WRITE|FILE_TXT);
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "MT4_READY_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        FileClose(file_handle);
        Print("Status file created: MT4 is ready for Python communication");
    }
    else
    {
        Print("Error creating status file: ", GetLastError());
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("Python Connector stopped. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for commands every 100ms
    if(TimeCurrent() - last_check >= check_interval/1000.0)
    {
        CheckForCommands();
        last_check = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Check for Python commands                                        |
//+------------------------------------------------------------------+
void CheckForCommands()
{
    if(!FileIsExist(commands_file))
        return;
        
    int file_handle = FileOpen(commands_file, FILE_READ|FILE_TXT);
    if(file_handle == INVALID_HANDLE)
        return;
        
    string command_json = "";
    while(!FileIsEnding(file_handle))
    {
        command_json += FileReadString(file_handle);
    }
    FileClose(file_handle);
    
    // Delete command file after reading
    FileDelete(commands_file);
    
    if(StringLen(command_json) > 0)
    {
        ProcessCommand(command_json);
    }
}

//+------------------------------------------------------------------+
//| Process Python command                                           |
//+------------------------------------------------------------------+
void ProcessCommand(string command_json)
{
    Print("Processing command: ", command_json);
    
    // Parse command (simplified JSON parsing)
    string command = ExtractJsonValue(command_json, "command");
    string id = ExtractJsonValue(command_json, "id");
    
    string response = "";
    
    if(command == "ping")
    {
        response = CreateResponse(id, true, "pong");
    }
    else if(command == "account_info")
    {
        response = CreateAccountInfoResponse(id);
    }
    else if(command == "get_price")
    {
        string symbol = ExtractJsonValue(command_json, "symbol");
        response = CreatePriceResponse(id, symbol);
    }
    else if(command == "place_order")
    {
        response = ProcessOrderCommand(command_json, id);
    }
    else if(command == "get_positions")
    {
        response = CreatePositionsResponse(id);
    }
    else if(command == "close_position")
    {
        string ticket = ExtractJsonValue(command_json, "ticket");
        response = ProcessClosePosition(id, ticket);
    }
    else
    {
        response = CreateResponse(id, false, "Unknown command: " + command);
    }
    
    // Write response
    WriteResponse(response);
}

//+------------------------------------------------------------------+
//| Create account info response                                     |
//+------------------------------------------------------------------+
string CreateAccountInfoResponse(string id)
{
    string data = "{";
    data += "\"balance\":" + DoubleToString(AccountBalance(), 2) + ",";
    data += "\"equity\":" + DoubleToString(AccountEquity(), 2) + ",";
    data += "\"margin\":" + DoubleToString(AccountMargin(), 2) + ",";
    data += "\"free_margin\":" + DoubleToString(AccountFreeMargin(), 2) + ",";
    data += "\"margin_level\":" + DoubleToString(AccountMargin() > 0 ? AccountEquity()/AccountMargin()*100 : 0, 2) + ",";
    data += "\"account_number\":\"" + IntegerToString(AccountNumber()) + "\",";
    data += "\"server\":\"" + AccountServer() + "\",";
    data += "\"currency\":\"" + AccountCurrency() + "\"";
    data += "}";
    
    return CreateResponse(id, true, data);
}

//+------------------------------------------------------------------+
//| Create price response                                            |
//+------------------------------------------------------------------+
string CreatePriceResponse(string id, string symbol)
{
    double bid = MarketInfo(symbol, MODE_BID);
    double ask = MarketInfo(symbol, MODE_ASK);
    double spread = ask - bid;
    
    if(bid == 0 || ask == 0)
    {
        return CreateResponse(id, false, "Symbol not found: " + symbol);
    }
    
    string data = "{";
    data += "\"bid\":" + DoubleToString(bid, Digits) + ",";
    data += "\"ask\":" + DoubleToString(ask, Digits) + ",";
    data += "\"spread\":" + DoubleToString(spread, Digits);
    data += "}";
    
    return CreateResponse(id, true, data);
}

//+------------------------------------------------------------------+
//| Process order command                                            |
//+------------------------------------------------------------------+
string ProcessOrderCommand(string command_json, string id)
{
    string symbol = ExtractJsonValue(command_json, "symbol");
    string order_type = ExtractJsonValue(command_json, "order_type");
    double volume = StringToDouble(ExtractJsonValue(command_json, "volume"));
    double stop_loss = StringToDouble(ExtractJsonValue(command_json, "stop_loss"));
    double take_profit = StringToDouble(ExtractJsonValue(command_json, "take_profit"));
    string comment = ExtractJsonValue(command_json, "comment");
    
    int cmd = -1;
    double price = 0;
    
    if(order_type == "BUY" || order_type == "buy")
    {
        cmd = OP_BUY;
        price = MarketInfo(symbol, MODE_ASK);
    }
    else if(order_type == "SELL" || order_type == "sell")
    {
        cmd = OP_SELL;
        price = MarketInfo(symbol, MODE_BID);
    }
    else
    {
        return CreateResponse(id, false, "Invalid order type: " + order_type);
    }
    
    int ticket = OrderSend(symbol, cmd, volume, price, 3, stop_loss, take_profit, comment, 0, 0, clrNONE);
    
    if(ticket > 0)
    {
        string data = "{";
        data += "\"ticket\":" + IntegerToString(ticket) + ",";
        data += "\"symbol\":\"" + symbol + "\",";
        data += "\"order_type\":\"" + order_type + "\",";
        data += "\"volume\":" + DoubleToString(volume, 2) + ",";
        data += "\"price\":" + DoubleToString(price, Digits) + ",";
        data += "\"stop_loss\":" + DoubleToString(stop_loss, Digits) + ",";
        data += "\"take_profit\":" + DoubleToString(take_profit, Digits);
        data += "}";
        
        Print("Order placed successfully. Ticket: ", ticket);
        return CreateResponse(id, true, data);
    }
    else
    {
        int error = GetLastError();
        Print("Order failed. Error: ", error);
        return CreateResponse(id, false, "Order failed. Error: " + IntegerToString(error));
    }
}

//+------------------------------------------------------------------+
//| Create positions response                                        |
//+------------------------------------------------------------------+
string CreatePositionsResponse(string id)
{
    string positions = "[";
    bool first = true;
    
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderType() == OP_BUY || OrderType() == OP_SELL)
            {
                if(!first) positions += ",";
                
                positions += "{";
                positions += "\"ticket\":" + IntegerToString(OrderTicket()) + ",";
                positions += "\"symbol\":\"" + OrderSymbol() + "\",";
                positions += "\"type\":\"" + (OrderType() == OP_BUY ? "BUY" : "SELL") + "\",";
                positions += "\"volume\":" + DoubleToString(OrderLots(), 2) + ",";
                positions += "\"open_price\":" + DoubleToString(OrderOpenPrice(), Digits) + ",";
                positions += "\"current_price\":" + DoubleToString(OrderType() == OP_BUY ? MarketInfo(OrderSymbol(), MODE_BID) : MarketInfo(OrderSymbol(), MODE_ASK), Digits) + ",";
                positions += "\"profit\":" + DoubleToString(OrderProfit(), 2);
                positions += "}";
                
                first = false;
            }
        }
    }
    
    positions += "]";
    
    return CreateResponse(id, true, positions);
}

//+------------------------------------------------------------------+
//| Process close position command                                   |
//+------------------------------------------------------------------+
string ProcessClosePosition(string id, string ticket_str)
{
    int ticket = StringToInteger(ticket_str);
    
    if(OrderSelect(ticket, SELECT_BY_TICKET))
    {
        double close_price = OrderType() == OP_BUY ? MarketInfo(OrderSymbol(), MODE_BID) : MarketInfo(OrderSymbol(), MODE_ASK);
        
        if(OrderClose(ticket, OrderLots(), close_price, 3, clrNONE))
        {
            Print("Position closed successfully. Ticket: ", ticket);
            return CreateResponse(id, true, "Position closed");
        }
        else
        {
            int error = GetLastError();
            Print("Close failed. Error: ", error);
            return CreateResponse(id, false, "Close failed. Error: " + IntegerToString(error));
        }
    }
    else
    {
        return CreateResponse(id, false, "Position not found: " + ticket_str);
    }
}

//+------------------------------------------------------------------+
//| Create JSON response                                             |
//+------------------------------------------------------------------+
string CreateResponse(string id, bool success, string data)
{
    string response = "{";
    response += "\"id\":" + id + ",";
    response += "\"success\":" + (success ? "true" : "false") + ",";
    response += "\"timestamp\":\"" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + "\",";
    response += "\"data\":" + (success ? data : "\"" + data + "\"");
    response += "}";
    
    return response;
}

//+------------------------------------------------------------------+
//| Write response to file                                           |
//+------------------------------------------------------------------+
void WriteResponse(string response)
{
    int file_handle = FileOpen(responses_file, FILE_WRITE|FILE_TXT);
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, response);
        FileClose(file_handle);
        Print("Response written: ", response);
    }
    else
    {
        Print("Error writing response: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Extract value from JSON string (simplified)                     |
//+------------------------------------------------------------------+
string ExtractJsonValue(string json, string key)
{
    string search_key = "\"" + key + "\":";
    int start_pos = StringFind(json, search_key);
    
    if(start_pos == -1)
        return "";
        
    start_pos += StringLen(search_key);
    
    // Skip whitespace
    while(start_pos < StringLen(json) && (StringGetChar(json, start_pos) == ' ' || StringGetChar(json, start_pos) == '\t'))
        start_pos++;
    
    int end_pos = start_pos;
    bool in_quotes = false;
    
    if(StringGetChar(json, start_pos) == '"')
    {
        in_quotes = true;
        start_pos++; // Skip opening quote
        end_pos = start_pos;
        
        while(end_pos < StringLen(json) && StringGetChar(json, end_pos) != '"')
            end_pos++;
    }
    else
    {
        while(end_pos < StringLen(json) && 
              StringGetChar(json, end_pos) != ',' && 
              StringGetChar(json, end_pos) != '}' &&
              StringGetChar(json, end_pos) != ' ')
            end_pos++;
    }
    
    return StringSubstr(json, start_pos, end_pos - start_pos);
}
