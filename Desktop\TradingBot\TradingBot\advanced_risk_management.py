#!/usr/bin/env python3
"""
Advanced Risk Management System
Dynamic position sizing, correlation analysis, portfolio heat mapping, drawdown protection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

@dataclass
class Position:
    symbol: str
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    entry_time: datetime
    stop_loss: float
    take_profit: float

@dataclass
class RiskMetrics:
    portfolio_var: float  # Value at Risk
    max_drawdown: float
    sharpe_ratio: float
    correlation_risk: float
    concentration_risk: float
    leverage_ratio: float

class AdvancedRiskManager:
    """Advanced risk management with dynamic sizing and portfolio analysis"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.positions: Dict[str, Position] = {}
        self.price_history: Dict[str, List[float]] = {}
        self.pnl_history: List[float] = []
        self.equity_curve: List[float] = []
        self.correlation_matrix = {}
        self.risk_metrics = RiskMetrics(0, 0, 0, 0, 0, 0)
        
        # Risk parameters
        self.max_portfolio_risk = config.get('max_portfolio_risk', 0.02)  # 2%
        self.max_correlation_exposure = config.get('max_correlation_exposure', 0.6)
        self.max_single_position_risk = config.get('max_single_position_risk', 0.005)  # 0.5%
        self.max_drawdown_limit = config.get('max_drawdown_limit', 0.1)  # 10%
        self.var_confidence = config.get('var_confidence', 0.95)  # 95% VaR
        
    def calculate_dynamic_position_size(self, symbol: str, entry_price: float, 
                                      stop_loss: float, account_balance: float,
                                      volatility: float, correlation_factor: float = 1.0) -> float:
        """Calculate optimal position size using multiple risk factors"""
        try:
            if stop_loss <= 0 or entry_price <= 0:
                return 0.01
            
            # Base risk per trade
            base_risk = account_balance * self.max_single_position_risk
            
            # Adjust for volatility (Kelly Criterion inspired)
            volatility_adjustment = max(0.5, min(2.0, 1.0 / max(volatility, 0.01)))
            
            # Adjust for correlation (reduce size if high correlation with existing positions)
            correlation_adjustment = max(0.3, 1.0 - correlation_factor)
            
            # Adjust for current portfolio heat
            portfolio_heat = self.calculate_portfolio_heat()
            heat_adjustment = max(0.2, 1.0 - portfolio_heat)
            
            # Adjust for recent performance
            performance_adjustment = self.calculate_performance_adjustment()
            
            # Calculate risk per unit
            risk_per_unit = abs(entry_price - stop_loss)
            
            # Calculate base position size
            base_size = base_risk / risk_per_unit
            
            # Apply all adjustments
            adjusted_size = (base_size * volatility_adjustment * 
                           correlation_adjustment * heat_adjustment * 
                           performance_adjustment)
            
            # Apply position limits
            max_size = account_balance * 0.1 / entry_price  # Max 10% of account in one position
            min_size = 0.01
            
            final_size = max(min_size, min(adjusted_size, max_size))
            
            logger.info(f"Position sizing for {symbol}:")
            logger.info(f"  Base size: {base_size:.4f}")
            logger.info(f"  Volatility adj: {volatility_adjustment:.2f}")
            logger.info(f"  Correlation adj: {correlation_adjustment:.2f}")
            logger.info(f"  Heat adj: {heat_adjustment:.2f}")
            logger.info(f"  Performance adj: {performance_adjustment:.2f}")
            logger.info(f"  Final size: {final_size:.4f}")
            
            return round(final_size, 2)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.01
    
    def calculate_portfolio_heat(self) -> float:
        """Calculate current portfolio heat (risk exposure)"""
        try:
            if not self.positions:
                return 0.0
            
            total_risk = 0.0
            for position in self.positions.values():
                # Calculate risk as distance to stop loss
                if position.stop_loss > 0:
                    risk = abs(position.current_price - position.stop_loss) * position.size
                    total_risk += risk
            
            # Normalize by account balance (assuming $10,000 base)
            account_balance = 10000  # This should come from broker
            heat = total_risk / account_balance
            
            return min(heat, 1.0)  # Cap at 100%
            
        except Exception as e:
            logger.error(f"Error calculating portfolio heat: {e}")
            return 0.0
    
    def calculate_correlation_matrix(self, symbols: List[str], 
                                   price_data: Dict[str, List[float]]) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between instruments"""
        try:
            correlation_matrix = {}
            
            for symbol1 in symbols:
                correlation_matrix[symbol1] = {}
                for symbol2 in symbols:
                    if symbol1 == symbol2:
                        correlation_matrix[symbol1][symbol2] = 1.0
                    else:
                        prices1 = price_data.get(symbol1, [])
                        prices2 = price_data.get(symbol2, [])
                        
                        if len(prices1) > 10 and len(prices2) > 10:
                            # Calculate returns
                            returns1 = np.diff(prices1) / prices1[:-1]
                            returns2 = np.diff(prices2) / prices2[:-1]
                            
                            # Ensure same length
                            min_len = min(len(returns1), len(returns2))
                            returns1 = returns1[-min_len:]
                            returns2 = returns2[-min_len:]
                            
                            # Calculate correlation
                            correlation = np.corrcoef(returns1, returns2)[0, 1]
                            correlation_matrix[symbol1][symbol2] = correlation if not np.isnan(correlation) else 0.0
                        else:
                            correlation_matrix[symbol1][symbol2] = 0.0
            
            self.correlation_matrix = correlation_matrix
            return correlation_matrix
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return {}
    
    def calculate_portfolio_var(self, confidence: float = 0.95) -> float:
        """Calculate Portfolio Value at Risk"""
        try:
            if len(self.pnl_history) < 30:
                return 0.0
            
            # Use historical simulation method
            returns = np.array(self.pnl_history[-100:])  # Last 100 periods
            
            # Calculate VaR at specified confidence level
            var_percentile = (1 - confidence) * 100
            var = np.percentile(returns, var_percentile)
            
            return abs(var)
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    def calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from equity curve"""
        try:
            if len(self.equity_curve) < 2:
                return 0.0
            
            equity = np.array(self.equity_curve)
            
            # Calculate running maximum
            running_max = np.maximum.accumulate(equity)
            
            # Calculate drawdown
            drawdown = (equity - running_max) / running_max
            
            # Return maximum drawdown (most negative value)
            max_dd = np.min(drawdown)
            
            return abs(max_dd)
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def calculate_sharpe_ratio(self, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            if len(self.pnl_history) < 30:
                return 0.0
            
            returns = np.array(self.pnl_history[-252:])  # Last year of daily returns
            
            if len(returns) == 0:
                return 0.0
            
            # Annualized return
            annual_return = np.mean(returns) * 252
            
            # Annualized volatility
            annual_volatility = np.std(returns) * np.sqrt(252)
            
            if annual_volatility == 0:
                return 0.0
            
            # Sharpe ratio
            sharpe = (annual_return - risk_free_rate) / annual_volatility
            
            return sharpe
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def calculate_performance_adjustment(self) -> float:
        """Calculate performance-based position size adjustment"""
        try:
            if len(self.pnl_history) < 10:
                return 1.0
            
            recent_performance = np.mean(self.pnl_history[-10:])
            
            # Reduce size after losses, increase after wins (but conservatively)
            if recent_performance < 0:
                # Reduce size by up to 50% after losses
                adjustment = max(0.5, 1.0 + (recent_performance * 0.1))
            else:
                # Increase size by up to 20% after wins
                adjustment = min(1.2, 1.0 + (recent_performance * 0.05))
            
            return adjustment
            
        except Exception as e:
            logger.error(f"Error calculating performance adjustment: {e}")
            return 1.0
    
    def check_correlation_risk(self, new_symbol: str, existing_positions: List[str]) -> float:
        """Check correlation risk when adding new position"""
        try:
            if not existing_positions or new_symbol not in self.correlation_matrix:
                return 0.0
            
            max_correlation = 0.0
            for existing_symbol in existing_positions:
                if existing_symbol in self.correlation_matrix.get(new_symbol, {}):
                    correlation = abs(self.correlation_matrix[new_symbol][existing_symbol])
                    max_correlation = max(max_correlation, correlation)
            
            return max_correlation
            
        except Exception as e:
            logger.error(f"Error checking correlation risk: {e}")
            return 0.0
    
    def should_reduce_risk(self) -> bool:
        """Determine if risk should be reduced based on current metrics"""
        try:
            # Check drawdown
            current_drawdown = self.calculate_max_drawdown()
            if current_drawdown > self.max_drawdown_limit:
                logger.warning(f"Drawdown limit exceeded: {current_drawdown:.2%}")
                return True
            
            # Check portfolio heat
            portfolio_heat = self.calculate_portfolio_heat()
            if portfolio_heat > 0.8:  # 80% of max risk
                logger.warning(f"Portfolio heat too high: {portfolio_heat:.2%}")
                return True
            
            # Check VaR
            var = self.calculate_portfolio_var()
            if var > self.max_portfolio_risk * 10000:  # Assuming $10k account
                logger.warning(f"VaR too high: ${var:.2f}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking risk reduction: {e}")
            return True  # Conservative: reduce risk on error
    
    def update_risk_metrics(self) -> RiskMetrics:
        """Update all risk metrics"""
        try:
            self.risk_metrics = RiskMetrics(
                portfolio_var=self.calculate_portfolio_var(),
                max_drawdown=self.calculate_max_drawdown(),
                sharpe_ratio=self.calculate_sharpe_ratio(),
                correlation_risk=max([max(row.values()) for row in self.correlation_matrix.values()]) if self.correlation_matrix else 0.0,
                concentration_risk=self.calculate_portfolio_heat(),
                leverage_ratio=len(self.positions) * 0.1  # Simplified leverage calculation
            )
            
            return self.risk_metrics
            
        except Exception as e:
            logger.error(f"Error updating risk metrics: {e}")
            return self.risk_metrics
    
    def get_risk_report(self) -> Dict[str, any]:
        """Generate comprehensive risk report"""
        try:
            self.update_risk_metrics()
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_metrics': {
                    'value_at_risk_95': self.risk_metrics.portfolio_var,
                    'max_drawdown': self.risk_metrics.max_drawdown,
                    'sharpe_ratio': self.risk_metrics.sharpe_ratio,
                    'portfolio_heat': self.risk_metrics.concentration_risk,
                    'correlation_risk': self.risk_metrics.correlation_risk
                },
                'position_count': len(self.positions),
                'risk_status': 'HIGH' if self.should_reduce_risk() else 'NORMAL',
                'recommendations': self.get_risk_recommendations()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating risk report: {e}")
            return {'error': str(e)}
    
    def get_risk_recommendations(self) -> List[str]:
        """Get risk management recommendations"""
        recommendations = []
        
        try:
            if self.risk_metrics.max_drawdown > 0.05:
                recommendations.append("Consider reducing position sizes - drawdown is elevated")
            
            if self.risk_metrics.correlation_risk > 0.7:
                recommendations.append("High correlation between positions - diversify")
            
            if self.risk_metrics.concentration_risk > 0.8:
                recommendations.append("Portfolio heat is high - reduce exposure")
            
            if len(self.positions) > 5:
                recommendations.append("Too many open positions - consider consolidation")
            
            if self.risk_metrics.sharpe_ratio < 0.5:
                recommendations.append("Poor risk-adjusted returns - review strategy")
            
            if not recommendations:
                recommendations.append("Risk levels are within acceptable parameters")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]
