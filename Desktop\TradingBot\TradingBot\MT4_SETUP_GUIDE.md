# MetaTrader 4 Setup Guide for Trading Bot

## Overview
This guide will help you set up MetaTrader 4 to work with the Python trading bot using socket communication.

## Prerequisites
- ✅ Python environment set up (completed)
- ✅ Dependencies installed (completed)
- ✅ Trading bot code ready (completed)
- 🔧 MetaTrader 4 installation (next step)

## Step 1: Install MetaTrader 4

### Download and Install MT4
1. **Download MetaTrader 4** from your broker (FBS):
   - Go to: https://fbs.com/trading-platforms/metatrader-4
   - Or download from: https://download.mql5.com/cdn/web/metaquotes.software.corp/mt4/mt4setup.exe

2. **Install MT4**:
   - Run the installer
   - Follow the installation wizard
   - Choose installation directory (default is fine)

3. **Login to your account**:
   - Server: `FBS-Demo`
   - Login: `********`
   - Password: `kTYg553z`

## Step 2: Install the Python Bridge Expert Advisor

### Copy the Expert Advisor
1. **Locate your MT4 data folder**:
   - In MT4, go to `File` → `Open Data Folder`
   - This opens the MT4 data directory

2. **Copy the Expert Advisor**:
   - Navigate to `MQL4/Experts/` folder
   - Copy the file `TradingBot/mt4_files/PythonBridge.mq4` to this folder

3. **Compile the Expert Advisor**:
   - In MT4, press `F4` to open MetaEditor
   - Open the `PythonBridge.mq4` file
   - Press `F7` to compile
   - Close MetaEditor

### Alternative: Manual Installation
If the above doesn't work, you can manually create the EA:

1. **Open MetaEditor** (F4 in MT4)
2. **Create New Expert Advisor**:
   - File → New → Expert Advisor (template)
   - Name: `PythonBridge`
   - Click Next → Next → Finish

3. **Replace the code**:
   - Delete all existing code
   - Copy the entire content from `TradingBot/mt4_files/PythonBridge.mq4`
   - Paste it into the editor
   - Save (Ctrl+S) and Compile (F7)

## Step 3: Configure MT4 Settings

### Enable Expert Advisors
1. **In MT4, go to**: `Tools` → `Options` → `Expert Advisors` tab
2. **Check these options**:
   - ✅ Allow automated trading
   - ✅ Allow DLL imports
   - ✅ Confirm DLL function calls (uncheck this for smoother operation)

3. **Click OK**

### Enable Auto Trading
1. **Click the "Auto Trading" button** in the toolbar (should turn green)
2. **Or press Ctrl+E**

## Step 4: Attach the Expert Advisor

### Attach to Chart
1. **Open a chart** (any symbol, e.g., EURUSD)
2. **In Navigator panel** (Ctrl+N if not visible):
   - Expand `Expert Advisors`
   - Find `PythonBridge`
   - Drag it onto the chart

3. **In the settings dialog**:
   - **Common tab**: Check "Allow live trading"
   - **Inputs tab**: Leave default settings
   - Click OK

4. **Verify it's running**:
   - You should see a smiley face 😊 in the top-right corner of the chart
   - Check the "Experts" tab at the bottom for messages

## Step 5: Test the Connection

### Run the Trading Bot
1. **Open Command Prompt** in the TradingBot folder
2. **Run the bot**:
   ```cmd
   TradingBot\env\Scripts\python.exe run_bot.py
   ```

3. **Check for connection messages**:
   ```
   ✓ Successfully connected to MT4 via socket
   ✓ Trading engine initialized
   Starting trading engine main loop...
   ```

### Troubleshooting Connection Issues

#### If connection fails:
1. **Check MT4 Expert Advisor**:
   - Look at the "Experts" tab in MT4
   - Should see: "Python Bridge EA starting..." and "Socket server started on port 9090"

2. **Check Windows Firewall**:
   - Windows might block the socket connection
   - Allow MT4 through Windows Firewall

3. **Check port availability**:
   - Make sure port 9090 is not used by other applications
   - You can change the port in both the EA code and config.json

#### If EA doesn't start:
1. **Check Expert Advisor settings**:
   - Make sure "Allow automated trading" is enabled
   - Make sure "Allow DLL imports" is enabled

2. **Check compilation**:
   - Recompile the EA in MetaEditor (F7)
   - Check for any compilation errors

## Step 6: Monitor the Bot

### What to Watch
1. **MT4 Experts Tab**: Shows EA messages and connection status
2. **Python Console**: Shows trading decisions and order execution
3. **MT4 Trade Tab**: Shows actual orders placed by the bot

### Expected Behavior
- Bot analyzes market every 60 seconds
- Places trades based on SMA crossover signals
- Manages risk with 2% per trade limit
- Closes positions after 24 hours or when targets are hit

## Alternative: Simulation Mode

If you can't get the socket connection working, the bot will run in **simulation mode**:
- All trading logic works the same
- Orders are logged but not actually placed
- Useful for testing strategies without real trades

## Support

### Common Issues:
1. **"Socket creation failed"**: Check Windows Firewall and antivirus
2. **"Bind failed"**: Port 9090 might be in use, try changing to 9091
3. **"Expert Advisor not working"**: Check EA settings and compilation

### Files Created:
- `TradingBot/mt4_files/PythonBridge.mq4` - Expert Advisor for MT4
- `TradingBot/src/brokers/mt4_socket.py` - Python socket client
- Updated configuration with socket settings

The trading bot is now configured to work with MetaTrader 4! 🚀
