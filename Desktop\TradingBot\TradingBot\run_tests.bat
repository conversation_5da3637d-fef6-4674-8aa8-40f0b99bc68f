@echo off
echo ================================================
echo Trading Bot Test and Run Script
echo ================================================

REM Set the working directory
cd /d "%~dp0"

echo.
echo Step 1: Testing Python environment...
TradingBot\env\Scripts\python.exe --version
if %errorlevel% neq 0 (
    echo ERROR: Python environment not working!
    pause
    exit /b 1
)

echo.
echo Step 2: Testing basic imports...
TradingBot\env\Scripts\python.exe -c "import pandas, numpy, MetaTrader5; print('Basic imports successful')"
if %errorlevel% neq 0 (
    echo ERROR: Import test failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Testing configuration...
if exist "TradingBot\config\config.json" (
    echo Configuration file found
) else (
    echo ERROR: Configuration file missing!
    pause
    exit /b 1
)

echo.
echo Step 4: Running the trading bot...
echo Press Ctrl+C to stop the bot when needed
echo.
TradingBot\env\Scripts\python.exe run_bot.py

echo.
echo ================================================
echo Bot execution completed
echo ================================================
pause
