#!/usr/bin/env python3
"""
Machine Learning Prediction Engine
Price prediction, pattern recognition, and adaptive strategy selection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import logging
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class MLPredictionEngine:
    """Machine Learning engine for trading predictions"""
    
    def __init__(self):
        self.price_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.pattern_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False
        self.feature_cache = {}
        
    def extract_features(self, prices: List[float], volumes: List[float] = None) -> np.array:
        """Extract features for ML models"""
        try:
            if len(prices) < 50:
                # Return default features if not enough data
                return np.array([0.0] * 25).reshape(1, -1)
            
            prices = np.array(prices)
            features = []
            
            # Price-based features
            features.extend([
                # Returns
                (prices[-1] - prices[-2]) / prices[-2] if len(prices) > 1 else 0,
                (prices[-1] - prices[-5]) / prices[-5] if len(prices) > 5 else 0,
                (prices[-1] - prices[-10]) / prices[-10] if len(prices) > 10 else 0,
                
                # Moving averages
                np.mean(prices[-5:]) / prices[-1] - 1,
                np.mean(prices[-10:]) / prices[-1] - 1,
                np.mean(prices[-20:]) / prices[-1] - 1,
                
                # Volatility
                np.std(prices[-10:]) / np.mean(prices[-10:]) if len(prices) > 10 else 0,
                np.std(prices[-20:]) / np.mean(prices[-20:]) if len(prices) > 20 else 0,
                
                # Price position in range
                (prices[-1] - np.min(prices[-20:])) / (np.max(prices[-20:]) - np.min(prices[-20:])) if len(prices) > 20 else 0.5,
                
                # Momentum indicators
                self._calculate_rsi(prices),
                self._calculate_macd_signal(prices),
                
                # Trend indicators
                self._calculate_trend_strength(prices),
                self._calculate_price_acceleration(prices),
            ])
            
            # Volume-based features (if available)
            if volumes and len(volumes) >= 10:
                volumes = np.array(volumes)
                features.extend([
                    volumes[-1] / np.mean(volumes[-10:]) - 1,  # Volume ratio
                    np.std(volumes[-10:]) / np.mean(volumes[-10:]),  # Volume volatility
                    np.corrcoef(prices[-10:], volumes[-10:])[0, 1] if len(prices) > 10 else 0,  # Price-volume correlation
                ])
            else:
                features.extend([0.0, 0.0, 0.0])
            
            # Time-based features
            now = datetime.now()
            features.extend([
                now.hour / 24.0,  # Hour of day
                now.weekday() / 6.0,  # Day of week
                now.day / 31.0,  # Day of month
            ])
            
            # Technical pattern features
            features.extend([
                self._detect_double_top_bottom(prices),
                self._detect_head_shoulders(prices),
                self._detect_triangle_pattern(prices),
                self._detect_flag_pattern(prices),
                self._calculate_support_resistance_strength(prices),
            ])
            
            # Ensure we have exactly 25 features
            while len(features) < 25:
                features.append(0.0)
            features = features[:25]
            
            return np.array(features).reshape(1, -1)
            
        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            return np.array([0.0] * 25).reshape(1, -1)
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate RSI"""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi / 100.0  # Normalize to 0-1
            
        except:
            return 0.5
    
    def _calculate_macd_signal(self, prices: List[float]) -> float:
        """Calculate MACD signal"""
        try:
            if len(prices) < 26:
                return 0.0
            
            ema_12 = self._ema(prices, 12)
            ema_26 = self._ema(prices, 26)
            macd = ema_12 - ema_26
            
            return np.tanh(macd / prices[-1])  # Normalize
            
        except:
            return 0.0
    
    def _ema(self, prices: List[float], period: int) -> float:
        """Calculate EMA"""
        try:
            if len(prices) < period:
                return np.mean(prices)
            
            multiplier = 2 / (period + 1)
            ema = prices[0]
            
            for price in prices[1:]:
                ema = (price * multiplier) + (ema * (1 - multiplier))
            
            return ema
        except:
            return prices[-1] if prices else 0.0
    
    def _calculate_trend_strength(self, prices: List[float]) -> float:
        """Calculate trend strength"""
        try:
            if len(prices) < 20:
                return 0.0
            
            # Linear regression slope
            x = np.arange(len(prices[-20:]))
            y = prices[-20:]
            slope = np.polyfit(x, y, 1)[0]
            
            return np.tanh(slope / np.mean(prices[-20:]) * 100)  # Normalize
            
        except:
            return 0.0
    
    def _calculate_price_acceleration(self, prices: List[float]) -> float:
        """Calculate price acceleration (second derivative)"""
        try:
            if len(prices) < 10:
                return 0.0
            
            recent_prices = prices[-10:]
            first_diff = np.diff(recent_prices)
            second_diff = np.diff(first_diff)
            
            acceleration = np.mean(second_diff)
            return np.tanh(acceleration / np.mean(recent_prices) * 1000)  # Normalize
            
        except:
            return 0.0
    
    def _detect_double_top_bottom(self, prices: List[float]) -> float:
        """Detect double top/bottom patterns"""
        try:
            if len(prices) < 30:
                return 0.0
            
            recent_prices = prices[-30:]
            
            # Find peaks and troughs
            peaks = []
            troughs = []
            
            for i in range(2, len(recent_prices) - 2):
                if (recent_prices[i] > recent_prices[i-1] and recent_prices[i] > recent_prices[i+1] and
                    recent_prices[i] > recent_prices[i-2] and recent_prices[i] > recent_prices[i+2]):
                    peaks.append((i, recent_prices[i]))
                
                if (recent_prices[i] < recent_prices[i-1] and recent_prices[i] < recent_prices[i+1] and
                    recent_prices[i] < recent_prices[i-2] and recent_prices[i] < recent_prices[i+2]):
                    troughs.append((i, recent_prices[i]))
            
            # Check for double top
            if len(peaks) >= 2:
                last_two_peaks = peaks[-2:]
                if abs(last_two_peaks[0][1] - last_two_peaks[1][1]) / last_two_peaks[0][1] < 0.02:
                    return -0.5  # Bearish signal
            
            # Check for double bottom
            if len(troughs) >= 2:
                last_two_troughs = troughs[-2:]
                if abs(last_two_troughs[0][1] - last_two_troughs[1][1]) / last_two_troughs[0][1] < 0.02:
                    return 0.5  # Bullish signal
            
            return 0.0
            
        except:
            return 0.0
    
    def _detect_head_shoulders(self, prices: List[float]) -> float:
        """Detect head and shoulders pattern"""
        try:
            if len(prices) < 40:
                return 0.0
            
            recent_prices = prices[-40:]
            
            # Find significant peaks
            peaks = []
            for i in range(5, len(recent_prices) - 5):
                if all(recent_prices[i] > recent_prices[i+j] for j in range(-5, 6) if j != 0):
                    peaks.append((i, recent_prices[i]))
            
            if len(peaks) >= 3:
                # Check if middle peak is highest (head) and outer peaks are similar (shoulders)
                last_three = peaks[-3:]
                head = last_three[1]
                left_shoulder = last_three[0]
                right_shoulder = last_three[2]
                
                if (head[1] > left_shoulder[1] and head[1] > right_shoulder[1] and
                    abs(left_shoulder[1] - right_shoulder[1]) / left_shoulder[1] < 0.03):
                    return -0.7  # Strong bearish signal
            
            return 0.0
            
        except:
            return 0.0
    
    def _detect_triangle_pattern(self, prices: List[float]) -> float:
        """Detect triangle patterns"""
        try:
            if len(prices) < 30:
                return 0.0
            
            recent_prices = prices[-30:]
            
            # Calculate trend lines for highs and lows
            highs = []
            lows = []
            
            for i in range(2, len(recent_prices) - 2):
                if recent_prices[i] > recent_prices[i-1] and recent_prices[i] > recent_prices[i+1]:
                    highs.append((i, recent_prices[i]))
                if recent_prices[i] < recent_prices[i-1] and recent_prices[i] < recent_prices[i+1]:
                    lows.append((i, recent_prices[i]))
            
            if len(highs) >= 2 and len(lows) >= 2:
                # Calculate slopes
                high_slope = (highs[-1][1] - highs[0][1]) / (highs[-1][0] - highs[0][0])
                low_slope = (lows[-1][1] - lows[0][1]) / (lows[-1][0] - lows[0][0])
                
                # Ascending triangle (bullish)
                if abs(high_slope) < 0.001 and low_slope > 0.001:
                    return 0.4
                
                # Descending triangle (bearish)
                if abs(low_slope) < 0.001 and high_slope < -0.001:
                    return -0.4
                
                # Symmetrical triangle (neutral, breakout direction matters)
                if high_slope < -0.001 and low_slope > 0.001:
                    return 0.1
            
            return 0.0
            
        except:
            return 0.0
    
    def _detect_flag_pattern(self, prices: List[float]) -> float:
        """Detect flag/pennant patterns"""
        try:
            if len(prices) < 25:
                return 0.0
            
            # Look for strong move followed by consolidation
            recent_prices = prices[-25:]
            
            # Check for strong initial move (first 10 periods)
            initial_move = (recent_prices[9] - recent_prices[0]) / recent_prices[0]
            
            # Check for consolidation (last 15 periods)
            consolidation_range = np.max(recent_prices[10:]) - np.min(recent_prices[10:])
            consolidation_ratio = consolidation_range / recent_prices[10]
            
            if abs(initial_move) > 0.02 and consolidation_ratio < 0.015:
                # Flag pattern detected
                return 0.3 if initial_move > 0 else -0.3
            
            return 0.0
            
        except:
            return 0.0
    
    def _calculate_support_resistance_strength(self, prices: List[float]) -> float:
        """Calculate strength of support/resistance levels"""
        try:
            if len(prices) < 20:
                return 0.0
            
            current_price = prices[-1]
            recent_prices = prices[-20:]
            
            # Count touches near current price level
            tolerance = current_price * 0.005  # 0.5% tolerance
            touches = sum(1 for p in recent_prices if abs(p - current_price) <= tolerance)
            
            # Normalize to 0-1 range
            strength = min(touches / 5.0, 1.0)
            
            return strength
            
        except:
            return 0.0
