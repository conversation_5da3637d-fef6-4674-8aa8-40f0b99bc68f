# scripts/setup_mt4.py
import os
import shutil
from datetime import datetime

def setup_mt4():
    """Setup MetaTrader 4 for Python integration"""
    print("Setting up MetaTrader 4 for Python integration...")
    
    # 1. Verify MT4 installation
    mt4_path = input("Enter path to MetaTrader 4 installation (e.g., C:/Program Files/MetaTrader 4): ")
    terminal_exe = os.path.join(mt4_path, 'terminal64.exe')
    
    if not os.path.exists(terminal_exe):
        print(f"Error: Could not find MetaTrader 4 terminal at {terminal_exe}")
        return False
        
    # 2. Enable DLL imports
    config_path = os.path.join(mt4_path, 'config')
    os.makedirs(config_path, exist_ok=True)
    
    with open(os.path.join(config_path, 'allowed_dlls.txt'), 'w') as f:
        f.write('*')  # Allow all DLLs (for development)
        
    # 3. Create expert advisor directory
    ea_dir = os.path.join(mt4_path, 'MQL4', 'Experts')
    os.makedirs(ea_dir, exist_ok=True)
    
    # 4. Copy Python integration EA
    src_ea = os.path.join(os.path.dirname(__file__), '..', 'mt4', 'PythonIntegrationEA.ex4')
    if os.path.exists(src_ea):
        shutil.copy(src_ea, ea_dir)
        print("Copied Python integration expert advisor to MT4")
    else:
        print("Warning: Python integration EA not found")
        
    print("\nSetup complete. Please restart MetaTrader 4.")
    print("To test the connection, run: python -m src.brokers.mt4_test")
    return True
    
if __name__ == '__main__':
    setup_mt4
