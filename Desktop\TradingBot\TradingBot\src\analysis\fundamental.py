# src/analysis/fundamental.py
import requests
from datetime import datetime
from typing import Dict, Any, Optional
import pandas as pd

class FundamentalAnalyzer:
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('fundamental', {})
        self.economic_calendar_api = self.config.get('economic_calendar_api')
        self.earnings_api = self.config.get('earnings_api')
        self.cache = {}
        self.cache_expiry = {}

    def analyze(self, symbol: str) -> Dict[str, Any]:
        """
        Analyze fundamental factors for a given symbol
        Returns dictionary with fundamental data and scores
        """
        analysis = {
            'economic_events': self.get_economic_events(symbol),
            'earnings': self.get_earnings_data(symbol),
            'sentiment': self.get_market_sentiment(symbol),
            'valuation': self.get_valuation_metrics(symbol)
        }
        
        # Calculate composite fundamental score
        analysis['score'] = self._calculate_fundamental_score(analysis)
        
        return analysis

    def get_economic_events(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get relevant economic calendar events for the symbol"""
        if not self.economic_calendar_api:
            return None
            
        # Map symbols to countries/regions
        country_map = {
            'XAUUSD': ['US', 'EU', 'CN'],
            'NAS100': ['US'],
            'US30': ['US'],
            'SPX500': ['US']
        }
        
        countries = country_map.get(symbol, ['US'])
        today = datetime.now().strftime('%Y-%m-%d')
        
        try:
            response = requests.get(
                f"{self.economic_calendar_api}?countries={','.join(countries)}&date={today}"
            )
            response.raise_for_status()
            events = response.json()
            
            # Filter for high-impact events only
            high_impact = [e for e in events if e.get('impact') == 'High']
            return {
                'count': len(high_impact),
                'events': high_impact[:3],  # Return top 3
                'next_event': self._get_next_scheduled_event(events)
            }
        except Exception as e:
            return {'error': str(e)}

    def get_earnings_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get earnings data for indices/components"""
        if not self.earnings_api or symbol not in ['NAS100', 'US30', 'SPX500']:
            return None
            
        try:
            # For indices, we might want earnings data for major components
            components_map = {
                'NAS100': ['AAPL', 'MSFT', 'AMZN', 'GOOGL', 'META'],
                'US30': ['AAPL', 'MSFT', 'GS', 'JPM', 'BA'],
                'SPX500': ['AAPL', 'MSFT', 'AMZN', 'GOOGL', 'TSLA']
            }
            
            components = components_map.get(symbol, [])
            earnings_data = []
            
            for ticker in components:
                response = requests.get(f"{self.earnings_api}/{ticker}")
                if response.status_code == 200:
                    earnings_data.append(response.json())
            
            return {
                'components': len(earnings_data),
                'positive_surprises': sum(1 for e in earnings_data if e.get('surprise') > 0),
                'negative_surprises': sum(1 for e in earnings_data if e.get('surprise') < 0),
                'upcoming_earnings': [e for e in earnings_data if e.get('date') >= datetime.now().strftime('%Y-%m-%d')]
            }
        except Exception as e:
            return {'error': str(e)}

    def get_market_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Get market sentiment indicators"""
        # This would typically come from a sentiment API
        return {
            'bullish_percent': 45.7,  # Example data
            'bearish_percent': 32.1,
            'neutral_percent': 22.2,
            'sentiment': 'neutral'  # or bullish/bearish
        }

    def get_valuation_metrics(self, symbol: str) -> Dict[str, Any]:
        """Get valuation metrics for indices"""
        # For indices, we might look at P/E ratios, yield curves, etc.
        if symbol == 'XAUUSD':
            return {
                'real_yields': self._get_real_yields(),
                'inflation_expectations': self._get_inflation_expectations(),
                'dollar_strength': self._get_dollar_index()
            }
        else:
            return {
                'pe_ratio': self._get_index_pe(symbol),
                'dividend_yield': self._get_dividend_yield(symbol),
                'risk_premium': self._get_equity_risk_premium()
            }

    def _calculate_fundamental_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate a composite fundamental score between 0 and 1"""
        score = 0.5  # Neutral baseline
        
        # Economic events impact
        events = analysis.get('economic_events', {})
        if events.get('count', 0) > 0:
            score -= 0.1  # High impact events generally increase uncertainty
        if events.get('next_event', {}).get('expected_impact') == 'Positive':
            score += 0.05
        
        # Earnings impact
        earnings = analysis.get('earnings', {})
        if earnings.get('positive_surprises', 0) > earnings.get('negative_surprises', 0):
            score += 0.1
        elif earnings.get('positive_surprises', 0) < earnings.get('negative_surprises', 0):
            score -= 0.1
        
        # Sentiment impact
        sentiment = analysis.get('sentiment', {})
        if sentiment.get('sentiment') == 'bullish':
            score += 0.05
        elif sentiment.get('sentiment') == 'bearish':
            score -= 0.05
        
        # Valuation impact (simplified)
        valuation = analysis.get('valuation', {})
        if symbol == 'XAUUSD':
            if valuation.get('real_yields', 0) < 0:
                score += 0.1  # Gold benefits from negative real yields
        else:
            if valuation.get('pe_ratio', 0) < valuation.get('historical_pe', 0):
                score += 0.1  # Undervalued
        
        return max(0, min(1, score))

    # Helper methods for economic data would be implemented here
    def _get_real_yields(self) -> float:
        """Get 10-year real yields (example)"""
        return -0.5  # Example value
        
    def _get_inflation_expectations(self) -> float:
        """Get inflation expectations"""
        return 2.5  # Example value
        
    def _get_dollar_index(self) -> float:
        """Get DXY index value"""
        return 95.0  # Example value
        
    def _get_index_pe(self, symbol: str) -> float:
        """Get index P/E ratio"""
        pe_map = {
            'NAS100': 28.5,
            'US30': 22.1,
            'SPX500': 25.3
        }
        return pe_map.get(symbol, 0)
        
    def _get_dividend_yield(self, symbol: str) -> float:
        """Get index dividend yield"""
        yield_map = {
            'NAS100': 0.8,
            'US30': 1.9,
            'SPX500': 1.5
        }
        return yield_map.get(symbol, 0)
        
    def _get_equity_risk_premium(self) -> float:
        """Get equity risk premium"""
        return 4.2  # Example value
        
    def _get_next_scheduled_event(self, events: list) -> Dict[str, Any]:
        """Get the next scheduled high-impact event"""
        future_events = [e for e in events if e.get('date') >= datetime.now().strftime('%Y-%m-%d')]
        if not future_events:
            return {}
        return sorted(future_events, key=lambda x: x['date'])[0]