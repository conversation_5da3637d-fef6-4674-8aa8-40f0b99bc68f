# src/strategies/news_based.py
from typing import Dict, Any
import pandas as pd
from ..data.news import NewsDataCollector
from .base_strategy import BaseStrategy

class NewsBasedStrategy(BaseStrategy):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.news_collector = NewsDataCollector(
            api_key=config.get('news_api_key'),
            sources=config.get('news_sources', ['Reuters', 'Bloomberg'])
        )
        self.min_sentiment_diff = config.get('min_sentiment_diff', 0.3)
        self.lookback_hours = config.get('lookback_hours', 24)
        
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform news sentiment analysis"""
        symbol = self.config.get('symbol')
        if not symbol:
            return {'error': 'No symbol configured'}
            
        # Get news articles
        articles = self.news_collector.get_news(
            symbol=symbol,
            lookback_hours=self.lookback_hours
        )
        
        if not articles:
            return {'error': 'No news articles found'}
            
        # Calculate aggregate sentiment
        sentiment_scores = [a['sentiment']['score'] for a in articles]
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
        
        return {
            'articles': articles,
            'avg_sentiment': avg_sentiment,
            'bullish_articles': sum(1 for a in articles if a['sentiment']['score'] > 0.6),
            'bearish_articles': sum(1 for a in articles if a['sentiment']['score'] < 0.4),
            'current_price': data['close'].values[-1]
        }
        
    def get_signal(self, analysis: Dict[str, Any]) -> str:
        """Generate trading signal based on news sentiment"""
        if 'error' in analysis:
            return 'HOLD'
            
        avg_sentiment = analysis['avg_sentiment']
        bullish = analysis['bullish_articles']
        bearish = analysis['bearish_articles']
        
        # Strong bullish sentiment
        if avg_sentiment > 0.5 + self.min_sentiment_diff and bullish > bearish * 2:
            return 'BUY'
            
        # Strong bearish sentiment
        if avg_sentiment < 0.5 - self.min_sentiment_diff and bearish > bullish * 2:
            return 'SELL'
            
        return 'HOLD'
