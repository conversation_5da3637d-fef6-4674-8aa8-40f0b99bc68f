#!/usr/bin/env python3
"""
Advanced Trading Bot with Multiple Strategies and Risk Management
Features:
- Multiple technical indicators (SMA, EMA, RSI, MACD, Bollinger Bands)
- Advanced risk management with stop-loss and take-profit
- Position sizing based on account balance
- Trade filtering and confirmation signals
- Real-time monitoring and position management
"""

import os
import time
import json
import logging
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
# import talib  # Commented out - using custom indicators instead

# Setup logging with Windows compatibility
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('trading_bot.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class AdvancedMT4Broker:
    """Advanced MT4 broker with enhanced features"""
    
    def __init__(self, config):
        self.config = config
        self.connected = False
        
        # Find MT4 data path
        mt4_paths = [
            os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal"),
            os.path.join(os.getcwd(), "mt4_files")
        ]
        
        self.mt4_data_path = None
        for path in mt4_paths:
            if os.path.exists(path):
                if "MetaQuotes" in path:
                    for folder in os.listdir(path):
                        terminal_path = os.path.join(path, folder, "MQL4", "Files")
                        if os.path.exists(terminal_path):
                            self.mt4_data_path = terminal_path
                            break
                else:
                    self.mt4_data_path = path
                break
        
        if not self.mt4_data_path:
            self.mt4_data_path = os.path.join(os.getcwd(), "mt4_files")
            os.makedirs(self.mt4_data_path, exist_ok=True)
        
        self.status_file = os.path.join(self.mt4_data_path, "mt4_status.txt")
        self.commands_file = os.path.join(self.mt4_data_path, "python_commands.txt")
        self.responses_file = os.path.join(self.mt4_data_path, "python_responses.txt")
        
        self.connect()
    
    def connect(self):
        """Enhanced connection with verification"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r') as f:
                    status = f.read()
                if "MT4_READY" in status:
                    # Test communication
                    test_response = self.send_command({"command": "ping"})
                    if test_response and test_response.get('success'):
                        self.connected = True
                        logger.info("[SUCCESS] Connected to MT4 - REAL TRADING MODE ACTIVE")
                        return True
                    else:
                        # Still connect even if ping fails - MT4 status shows ready
                        self.connected = True
                        logger.info("[SUCCESS] Connected to MT4 - REAL TRADING MODE ACTIVE (status file OK)")
                        return True

            logger.warning("[WARNING] MT4 not ready - SIMULATION MODE")
            return False
        except Exception as e:
            logger.error(f"Connection error: {e}")
            return False
    
    def send_command(self, command, timeout=10):
        """Enhanced command sending with better error handling"""
        try:
            if not self.connected:
                return None
            
            # Clean up any existing response file
            if os.path.exists(self.responses_file):
                os.remove(self.responses_file)
            
            # Send command
            with open(self.commands_file, 'w') as f:
                json.dump(command, f)
            
            # Wait for response
            for i in range(timeout * 2):  # Check every 0.5 seconds
                if os.path.exists(self.responses_file):
                    time.sleep(0.1)  # Ensure file is fully written
                    try:
                        with open(self.responses_file, 'r') as f:
                            response = json.load(f)
                        os.remove(self.responses_file)
                        return response
                    except:
                        pass
                time.sleep(0.5)
            
            logger.warning(f"Command timeout: {command}")
            return None
            
        except Exception as e:
            logger.error(f"Command error: {e}")
            return None
    
    def get_account_info(self):
        """Get enhanced account information"""
        if self.connected:
            response = self.send_command({"command": "account_info"})
            if response and response.get('success'):
                return response['data']
        
        # Simulation fallback
        return {
            'login': self.config['login'],
            'balance': 10000.0,
            'equity': 10000.0,
            'margin': 0.0,
            'margin_free': 10000.0,
            'currency': 'USD',
            'server': self.config['server'],
            'leverage': 100
        }
    
    def get_price(self, symbol):
        """Get current price with validation"""
        if self.connected:
            response = self.send_command({"command": "get_price", "symbol": symbol})
            if response and response.get('success'):
                data = response['data']
                # Validate price data
                if data.get('bid', 0) > 0 and data.get('ask', 0) > 0:
                    return data
        
        # Enhanced simulation prices
        mock_prices = {
            'XAUUSD': {'bid': 2650.50, 'ask': 2650.70, 'spread': 0.20},
            'EURUSD': {'bid': 1.0850, 'ask': 1.0852, 'spread': 0.0002},
            'GBPUSD': {'bid': 1.2650, 'ask': 1.2652, 'spread': 0.0002},
            'NAS100': {'bid': 15000.0, 'ask': 15001.0, 'spread': 1.0},
            'US30': {'bid': 35000.0, 'ask': 35001.0, 'spread': 1.0}
        }
        return mock_prices.get(symbol, {'bid': 1.0000, 'ask': 1.0001, 'spread': 0.0001})
    
    def place_order_with_sl_tp(self, symbol, order_type, volume, stop_loss=None, take_profit=None, comment=""):
        """Place order with stop loss and take profit"""
        try:
            if self.connected:
                cmd = {
                    "command": "place_order",
                    "symbol": symbol,
                    "order_type": order_type,
                    "volume": volume,
                    "stop_loss": stop_loss,
                    "take_profit": take_profit,
                    "comment": comment
                }
                
                response = self.send_command(cmd, timeout=15)
                if response and response.get('success'):
                    data = response['data']
                    logger.info(f"[REAL ORDER] {symbol} {order_type} {volume} lots")
                    logger.info(f"   Ticket: {data.get('ticket')}, Price: {data.get('price')}")
                    if stop_loss:
                        logger.info(f"   Stop Loss: {stop_loss}")
                    if take_profit:
                        logger.info(f"   Take Profit: {take_profit}")
                    return data
                else:
                    logger.error(f"[ERROR] Order failed: {response}")
                    return None
            
            # Enhanced simulation
            price_data = self.get_price(symbol)
            entry_price = price_data['ask'] if order_type == 'BUY' else price_data['bid']
            
            logger.info(f"[SIMULATION] {symbol} {order_type} {volume} lots @ {entry_price}")
            if stop_loss:
                logger.info(f"   Stop Loss: {stop_loss}")
            if take_profit:
                logger.info(f"   Take Profit: {take_profit}")
            
            return {
                'ticket': int(time.time()),
                'price': entry_price,
                'volume': volume,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Order placement error: {e}")
            return None
    
    def get_positions(self):
        """Get all open positions"""
        if self.connected:
            response = self.send_command({"command": "get_positions"})
            if response and response.get('success'):
                return response['data']
        return []
    
    def close_position(self, ticket):
        """Close a specific position"""
        if self.connected:
            response = self.send_command({"command": "close_position", "ticket": ticket})
            if response and response.get('success'):
                logger.info(f"[SUCCESS] Position {ticket} closed")
                return True

        logger.info(f"[SIMULATION] Position {ticket} closed")
        return True

class AdvancedAnalyzer:
    """Advanced market analyzer with multiple indicators"""
    
    def __init__(self):
        self.history_cache = {}
        self.cache_timeout = 300  # 5 minutes
    
    def generate_price_history(self, symbol, periods=200):
        """Generate realistic price history for analysis"""
        # Check cache first
        cache_key = f"{symbol}_{periods}"
        if cache_key in self.history_cache:
            cached_data, timestamp = self.history_cache[cache_key]
            if time.time() - timestamp < self.cache_timeout:
                return cached_data
        
        # Base prices for different instruments
        base_prices = {
            'XAUUSD': 2650.0,
            'EURUSD': 1.0850,
            'GBPUSD': 1.2650,
            'NAS100': 15000.0,
            'US30': 35000.0
        }
        
        base_price = base_prices.get(symbol, 1.0000)
        
        # Generate realistic price movement
        prices = []
        current_price = base_price
        
        for i in range(periods):
            # Add trend and noise
            trend = np.sin(i * 0.1) * 0.001  # Long-term trend
            noise = np.random.normal(0, 0.002)  # Random noise
            volatility = 0.005 if 'USD' in symbol else 0.01  # Different volatility for different instruments
            
            change = (trend + noise) * volatility
            current_price *= (1 + change)
            prices.append(current_price)
        
        # Cache the result
        self.history_cache[cache_key] = (prices, time.time())
        return prices
    
    def calculate_sma(self, prices, period):
        """Calculate Simple Moving Average"""
        if len(prices) < period:
            return prices[-1]
        return np.mean(prices[-period:])

    def calculate_ema(self, prices, period):
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return prices[-1]

        multiplier = 2 / (period + 1)
        ema = prices[0]

        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))

        return ema

    def calculate_rsi(self, prices, period=14):
        """Calculate Relative Strength Index"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def calculate_macd(self, prices):
        """Calculate MACD"""
        if len(prices) < 26:
            return 0.0, 0.0, 0.0

        ema_12 = self.calculate_ema(prices, 12)
        ema_26 = self.calculate_ema(prices, 26)

        macd_line = ema_12 - ema_26

        # Simple signal line (9-period EMA of MACD)
        macd_signal = macd_line * 0.8  # Simplified
        macd_histogram = macd_line - macd_signal

        return macd_line, macd_signal, macd_histogram

    def calculate_bollinger_bands(self, prices, period=20, std_dev=2):
        """Calculate Bollinger Bands"""
        if len(prices) < period:
            current_price = prices[-1]
            return current_price * 1.02, current_price, current_price * 0.98

        sma = self.calculate_sma(prices, period)
        std = np.std(prices[-period:])

        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)

        return upper_band, sma, lower_band

    def calculate_indicators(self, prices):
        """Calculate multiple technical indicators using custom functions"""
        prices = np.array(prices, dtype=float)

        indicators = {}

        try:
            # Moving Averages
            indicators['sma_10'] = self.calculate_sma(prices, 10)
            indicators['sma_20'] = self.calculate_sma(prices, 20)
            indicators['sma_50'] = self.calculate_sma(prices, 50)
            indicators['ema_12'] = self.calculate_ema(prices, 12)
            indicators['ema_26'] = self.calculate_ema(prices, 26)

            # RSI
            indicators['rsi'] = self.calculate_rsi(prices, 14)

            # MACD
            macd, macd_signal, macd_hist = self.calculate_macd(prices)
            indicators['macd'] = macd
            indicators['macd_signal'] = macd_signal
            indicators['macd_histogram'] = macd_hist

            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(prices)
            indicators['bb_upper'] = bb_upper
            indicators['bb_lower'] = bb_lower
            indicators['bb_middle'] = bb_middle

        except Exception as e:
            logger.warning(f"Indicator calculation error: {e}")
            # Fallback values
            current_price = prices[-1]
            indicators = {
                'sma_10': current_price, 'sma_20': current_price, 'sma_50': current_price,
                'ema_12': current_price, 'ema_26': current_price,
                'rsi': 50.0, 'macd': 0.0, 'macd_signal': 0.0, 'macd_histogram': 0.0,
                'bb_upper': current_price * 1.02, 'bb_lower': current_price * 0.98, 'bb_middle': current_price
            }

        return indicators
    
    def analyze_symbol_advanced(self, symbol, broker):
        """Advanced multi-indicator analysis"""
        try:
            # Get current price
            price_data = broker.get_price(symbol)
            current_price = price_data['bid']
            
            # Generate price history
            prices = self.generate_price_history(symbol)
            prices[-1] = current_price  # Use real current price
            
            # Calculate indicators
            indicators = self.calculate_indicators(prices)
            
            # Multi-factor signal analysis
            signals = []
            confidence_factors = []
            
            # 1. Moving Average Crossover
            if indicators['sma_10'] > indicators['sma_20'] * 1.001:  # 0.1% threshold
                signals.append('BUY')
                confidence_factors.append(0.3)
            elif indicators['sma_10'] < indicators['sma_20'] * 0.999:
                signals.append('SELL')
                confidence_factors.append(0.3)
            
            # 2. MACD Signal
            if indicators['macd'] > indicators['macd_signal'] and indicators['macd_histogram'] > 0:
                signals.append('BUY')
                confidence_factors.append(0.25)
            elif indicators['macd'] < indicators['macd_signal'] and indicators['macd_histogram'] < 0:
                signals.append('SELL')
                confidence_factors.append(0.25)
            
            # 3. RSI Confirmation
            rsi = indicators['rsi']
            if 30 < rsi < 70:  # Not overbought/oversold
                if rsi > 55:
                    signals.append('BUY')
                    confidence_factors.append(0.2)
                elif rsi < 45:
                    signals.append('SELL')
                    confidence_factors.append(0.2)
            
            # 4. Bollinger Bands
            if current_price < indicators['bb_lower'] * 1.001:  # Near lower band
                signals.append('BUY')
                confidence_factors.append(0.15)
            elif current_price > indicators['bb_upper'] * 0.999:  # Near upper band
                signals.append('SELL')
                confidence_factors.append(0.15)
            
            # 5. Trend Confirmation
            if indicators['sma_10'] > indicators['sma_50']:
                if 'BUY' in signals:
                    confidence_factors.append(0.1)
            elif indicators['sma_10'] < indicators['sma_50']:
                if 'SELL' in signals:
                    confidence_factors.append(0.1)
            
            # Determine final signal
            buy_signals = signals.count('BUY')
            sell_signals = signals.count('SELL')
            
            if buy_signals > sell_signals and buy_signals >= 2:
                final_signal = 'BUY'
                confidence = min(sum(confidence_factors), 0.95)
            elif sell_signals > buy_signals and sell_signals >= 2:
                final_signal = 'SELL'
                confidence = min(sum(confidence_factors), 0.95)
            else:
                final_signal = 'HOLD'
                confidence = 0.3
            
            return {
                'symbol': symbol,
                'signal': final_signal,
                'confidence': confidence,
                'current_price': current_price,
                'indicators': indicators,
                'rsi': rsi,
                'spread': price_data.get('spread', 0.001)
            }
            
        except Exception as e:
            logger.error(f"Analysis error for {symbol}: {e}")
            return {
                'symbol': symbol,
                'signal': 'HOLD',
                'confidence': 0.0,
                'current_price': 0.0,
                'indicators': {},
                'rsi': 50.0,
                'spread': 0.001
            }

class RiskManager:
    """Advanced risk management system"""

    def __init__(self, config):
        self.max_risk_per_trade = config.get('max_risk_per_trade', 0.02)  # 2%
        self.max_daily_loss = config.get('max_daily_loss', 0.05)  # 5%
        self.max_positions = config.get('max_positions', 3)
        self.min_risk_reward_ratio = config.get('min_risk_reward_ratio', 1.5)  # 1:1.5

        self.daily_pnl = 0.0
        self.daily_trades = 0
        self.start_balance = 0.0

    def calculate_position_size(self, account_balance, entry_price, stop_loss_price, symbol):
        """Calculate optimal position size based on risk"""
        try:
            if not stop_loss_price or stop_loss_price <= 0:
                return 0.01  # Minimum size if no stop loss

            # Calculate risk per pip/point
            risk_amount = account_balance * self.max_risk_per_trade

            # Calculate stop loss distance
            sl_distance = abs(entry_price - stop_loss_price)

            # Different pip values for different instruments
            pip_values = {
                'XAUUSD': 0.01,  # $0.01 per 0.01 movement for 1 lot
                'EURUSD': 0.0001,  # $10 per pip for 1 lot
                'GBPUSD': 0.0001,
                'NAS100': 0.25,  # $0.25 per point for 1 lot
                'US30': 1.0     # $1 per point for 1 lot
            }

            pip_value = pip_values.get(symbol, 0.0001)

            # Calculate position size
            if 'USD' in symbol:
                # For forex pairs
                position_size = risk_amount / (sl_distance / pip_value * 10)
            else:
                # For indices
                position_size = risk_amount / (sl_distance / pip_value)

            # Apply limits
            position_size = max(0.01, min(position_size, 1.0))  # Between 0.01 and 1.0 lots

            return round(position_size, 2)

        except Exception as e:
            logger.error(f"Position size calculation error: {e}")
            return 0.01

    def calculate_stop_loss_take_profit(self, symbol, signal, entry_price, analysis):
        """Calculate optimal stop loss and take profit levels"""
        try:
            indicators = analysis.get('indicators', {})

            # Base stop loss calculation using ATR-like approach
            if 'USD' in symbol:
                # Forex pairs - use percentage-based stops
                base_sl_distance = entry_price * 0.005  # 0.5%
                base_tp_distance = base_sl_distance * self.min_risk_reward_ratio
            else:
                # Indices - use point-based stops
                if symbol == 'XAUUSD':
                    base_sl_distance = 15.0  # $15 for gold
                elif symbol in ['NAS100', 'US30']:
                    base_sl_distance = 50.0  # 50 points for indices
                else:
                    base_sl_distance = entry_price * 0.01  # 1%

                base_tp_distance = base_sl_distance * self.min_risk_reward_ratio

            # Adjust based on technical levels
            if signal == 'BUY':
                # Use support levels for stop loss
                support_level = indicators.get('bb_lower', entry_price * 0.98)
                sl_technical = min(support_level, entry_price - base_sl_distance)
                stop_loss = max(sl_technical, entry_price * 0.99)  # Don't go below 1%

                # Use resistance for take profit
                resistance_level = indicators.get('bb_upper', entry_price * 1.02)
                take_profit = max(resistance_level, entry_price + base_tp_distance)

            else:  # SELL
                # Use resistance levels for stop loss
                resistance_level = indicators.get('bb_upper', entry_price * 1.02)
                sl_technical = max(resistance_level, entry_price + base_sl_distance)
                stop_loss = min(sl_technical, entry_price * 1.01)  # Don't go above 1%

                # Use support for take profit
                support_level = indicators.get('bb_lower', entry_price * 0.98)
                take_profit = min(support_level, entry_price - base_tp_distance)

            return round(stop_loss, 5), round(take_profit, 5)

        except Exception as e:
            logger.error(f"SL/TP calculation error: {e}")
            # Fallback to simple percentage-based levels
            if signal == 'BUY':
                return entry_price * 0.99, entry_price * 1.03
            else:
                return entry_price * 1.01, entry_price * 0.97

    def check_daily_limits(self, account_balance):
        """Check if daily trading limits are exceeded"""
        if self.start_balance == 0:
            self.start_balance = account_balance

        daily_loss_pct = abs(self.daily_pnl) / self.start_balance

        if daily_loss_pct >= self.max_daily_loss:
            logger.warning(f"[WARNING] Daily loss limit reached: {daily_loss_pct:.2%}")
            return False

        if self.daily_trades >= 10:  # Max 10 trades per day
            logger.warning("[WARNING] Daily trade limit reached")
            return False

        return True

class AdvancedTradingBot:
    """Advanced trading bot with multiple strategies and risk management"""

    def __init__(self):
        # Enhanced configuration
        self.config = {
            'server': 'FBS-Demo',
            'login': ********,
            'password': 'kTYg$53Z',
            'max_risk_per_trade': 0.015,  # 1.5% risk per trade
            'max_daily_loss': 0.04,       # 4% daily loss limit
            'max_positions': 3,           # Max 3 concurrent positions
            'min_risk_reward_ratio': 2.0, # 1:2 risk-reward
            'min_confidence': 0.6         # Minimum 60% confidence to trade
        }

        # Enhanced instrument list with better symbols
        self.instruments = ['XAUUSD', 'EURUSD', 'GBPUSD', 'NAS100', 'US30']
        self.scan_interval = 30  # Scan every 30 seconds for better opportunities

        # Initialize components
        self.broker = AdvancedMT4Broker(self.config)
        self.analyzer = AdvancedAnalyzer()
        self.risk_manager = RiskManager(self.config)

        # Position tracking
        self.open_positions = {}
        self.last_trade_time = {}

        logger.info("[INIT] Advanced Trading Bot initialized")
        logger.info(f"   Risk per trade: {self.config['max_risk_per_trade']:.1%}")
        logger.info(f"   Risk-reward ratio: 1:{self.config['min_risk_reward_ratio']}")
        logger.info(f"   Minimum confidence: {self.config['min_confidence']:.1%}")

    def run(self):
        """Enhanced main trading loop"""
        logger.info("[START] Starting Advanced Trading Bot...")
        logger.info("[INFO] Features: Multi-indicator analysis, Risk management, SL/TP automation")
        logger.info("[INFO] Press Ctrl+C to stop")

        try:
            while True:
                self.trading_cycle()
                time.sleep(self.scan_interval)

        except KeyboardInterrupt:
            logger.info("[STOP] Trading bot stopped by user")
            self.cleanup()
        except Exception as e:
            logger.error(f"[ERROR] Bot error: {e}")
            self.cleanup()

    def trading_cycle(self):
        """Enhanced trading cycle with better logic"""
        try:
            logger.info("[SCAN] Advanced Market Scan...")

            # Get account info
            account = self.broker.get_account_info()
            balance = account['balance']
            equity = account['equity']

            logger.info(f"[ACCOUNT] Balance: ${balance:.2f} | Equity: ${equity:.2f}")

            # Check daily limits
            if not self.risk_manager.check_daily_limits(balance):
                logger.warning("[WARNING] Daily limits reached - no new trades")
                return

            # Update existing positions
            self.monitor_positions()

            # Check if we can open new positions
            if len(self.open_positions) >= self.config['max_positions']:
                logger.info(f"[INFO] Max positions reached ({len(self.open_positions)}/{self.config['max_positions']})")
                return

            # Analyze all instruments
            opportunities = []

            for symbol in self.instruments:
                # Avoid trading same symbol too frequently
                if symbol in self.last_trade_time:
                    time_since_last = time.time() - self.last_trade_time[symbol]
                    if time_since_last < 300:  # 5 minutes cooldown
                        continue

                analysis = self.analyzer.analyze_symbol_advanced(symbol, self.broker)

                logger.info(f"{symbol}: {analysis['signal']} "
                          f"(conf: {analysis['confidence']:.1%}, "
                          f"RSI: {analysis['rsi']:.1f}, "
                          f"price: {analysis['current_price']:.5f})")

                # Filter high-quality opportunities
                if (analysis['signal'] in ['BUY', 'SELL'] and
                    analysis['confidence'] >= self.config['min_confidence'] and
                    symbol not in self.open_positions):

                    opportunities.append(analysis)

            # Sort by confidence and take the best opportunity
            if opportunities:
                opportunities.sort(key=lambda x: x['confidence'], reverse=True)
                best_opportunity = opportunities[0]
                self.execute_trade(best_opportunity, balance)

        except Exception as e:
            logger.error(f"Trading cycle error: {e}")

    def execute_trade(self, analysis, account_balance):
        """Execute trade with advanced risk management"""
        try:
            symbol = analysis['symbol']
            signal = analysis['signal']
            confidence = analysis['confidence']
            entry_price = analysis['current_price']

            # Calculate stop loss and take profit
            stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                symbol, signal, entry_price, analysis
            )

            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                account_balance, entry_price, stop_loss, symbol
            )

            # Execute the trade
            result = self.broker.place_order_with_sl_tp(
                symbol=symbol,
                order_type=signal,
                volume=position_size,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"Advanced Bot - Conf: {confidence:.1%}"
            )

            if result and result.get('success'):
                # Track the position
                self.open_positions[symbol] = {
                    'ticket': result['ticket'],
                    'type': signal,
                    'volume': position_size,
                    'entry_price': result['price'],
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'entry_time': datetime.now(),
                    'confidence': confidence
                }

                self.last_trade_time[symbol] = time.time()
                self.risk_manager.daily_trades += 1

                # Calculate risk metrics
                risk_amount = account_balance * self.config['max_risk_per_trade']
                potential_profit = abs(take_profit - entry_price) * position_size * 100000  # Rough calculation

                logger.info(f"[TRADE] EXECUTED: {symbol} {signal}")
                logger.info(f"   Risk: ${risk_amount:.2f} | Potential: ${potential_profit:.2f}")
                logger.info(f"   Confidence: {confidence:.1%} | Size: {position_size} lots")

        except Exception as e:
            logger.error(f"Trade execution error: {e}")

    def monitor_positions(self):
        """Monitor and manage open positions"""
        try:
            current_positions = self.broker.get_positions()

            # Update position tracking
            for symbol in list(self.open_positions.keys()):
                position = self.open_positions[symbol]

                # Check if position is still open
                position_exists = any(p.get('ticket') == position['ticket'] for p in current_positions)

                if not position_exists:
                    logger.info(f"[CLOSED] Position closed: {symbol} (Ticket: {position['ticket']})")
                    del self.open_positions[symbol]
                    continue

                # Check for manual exit conditions (optional)
                time_in_trade = datetime.now() - position['entry_time']
                if time_in_trade.total_seconds() > 86400:  # 24 hours
                    logger.info(f"[TIME] Closing position due to time limit: {symbol}")
                    self.broker.close_position(position['ticket'])

        except Exception as e:
            logger.error(f"Position monitoring error: {e}")

    def cleanup(self):
        """Cleanup when stopping the bot"""
        logger.info("[CLEANUP] Cleaning up...")
        logger.info(f"[STATS] Final stats: {self.risk_manager.daily_trades} trades today")
        if self.open_positions:
            logger.info(f"[POSITIONS] Open positions: {len(self.open_positions)}")

def main():
    """Main function"""
    print("=" * 70)
    print("ADVANCED TRADING BOT v2.0")
    print("=" * 70)
    print("Features:")
    print("   • Multi-indicator analysis (SMA, EMA, RSI, MACD, Bollinger Bands)")
    print("   • Automatic stop-loss and take-profit")
    print("   • Advanced risk management (1.5% risk per trade)")
    print("   • Position sizing based on account balance")
    print("   • Daily loss limits and trade frequency controls")
    print("   • Real-time position monitoring")
    print("=" * 70)

    try:
        bot = AdvancedTradingBot()
        bot.run()
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")

if __name__ == "__main__":
    main()
