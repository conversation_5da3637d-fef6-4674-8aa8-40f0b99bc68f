#!/usr/bin/env python3
"""
Simple MT4 connection test without complex imports
"""

import os
import time
import json

def test_mt4_files():
    """Test MT4 file communication"""
    print("=" * 60)
    print("SIMPLE MT4 FILE COMMUNICATION TEST")
    print("=" * 60)
    
    # Check if we can find MT4 data folder
    mt4_paths = [
        os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal"),
        os.path.join(os.getcwd(), "mt4_files")
    ]
    
    mt4_data_path = None
    for path in mt4_paths:
        if os.path.exists(path):
            if "MetaQuotes" in path:
                # Look for terminal folders
                for folder in os.listdir(path):
                    terminal_path = os.path.join(path, folder, "MQL4", "Files")
                    if os.path.exists(terminal_path):
                        mt4_data_path = terminal_path
                        break
            else:
                mt4_data_path = path
            break
    
    if not mt4_data_path:
        mt4_data_path = os.path.join(os.getcwd(), "mt4_files")
        os.makedirs(mt4_data_path, exist_ok=True)
    
    print(f"MT4 communication path: {mt4_data_path}")
    
    # File paths
    status_file = os.path.join(mt4_data_path, "mt4_status.txt")
    commands_file = os.path.join(mt4_data_path, "python_commands.txt")
    responses_file = os.path.join(mt4_data_path, "python_responses.txt")
    
    # Test 1: Check if MT4 status file exists
    print("\n1. Checking MT4 status...")
    if os.path.exists(status_file):
        try:
            with open(status_file, 'r') as f:
                status = f.read()
            print("✓ MT4 status file found!")
            print("Status content:")
            for line in status.strip().split('\n'):
                print(f"   {line}")
            
            if "MT4_READY" in status:
                print("✓ MT4 Expert Advisor is running and ready!")
                mt4_connected = True
            else:
                print("⚠ MT4 Expert Advisor might not be running")
                mt4_connected = False
        except Exception as e:
            print(f"✗ Error reading status file: {e}")
            mt4_connected = False
    else:
        print("⚠ MT4 status file not found")
        print("This means:")
        print("  - MetaTrader 4 is not running, OR")
        print("  - PythonBridge Expert Advisor is not attached to a chart")
        mt4_connected = False
    
    # Test 2: Try to send a command to MT4
    print("\n2. Testing command communication...")
    if mt4_connected:
        try:
            # Send a ping command
            command = {"command": "ping"}
            with open(commands_file, 'w') as f:
                json.dump(command, f)
            
            print("✓ Command sent to MT4")
            
            # Wait for response
            print("Waiting for MT4 response...")
            for i in range(10):  # Wait up to 10 seconds
                if os.path.exists(responses_file):
                    time.sleep(0.1)  # Give it a moment to finish writing
                    try:
                        with open(responses_file, 'r') as f:
                            response = json.load(f)
                        os.remove(responses_file)  # Clean up
                        
                        print("✓ Received response from MT4:")
                        print(f"   {response}")
                        
                        if response.get('success'):
                            print("✓ MT4 communication working perfectly!")
                            return True
                        else:
                            print("⚠ MT4 responded but with an error")
                            return False
                            
                    except Exception as e:
                        print(f"✗ Error reading response: {e}")
                        return False
                
                time.sleep(1)
                print(f"   Waiting... ({i+1}/10)")
            
            print("✗ No response from MT4 (timeout)")
            print("This might mean:")
            print("  - Expert Advisor is not processing commands")
            print("  - File permissions issue")
            return False
            
        except Exception as e:
            print(f"✗ Error sending command: {e}")
            return False
    else:
        print("⚠ Skipping command test (MT4 not ready)")
        return False

def test_account_simulation():
    """Test simulation mode"""
    print("\n3. Testing simulation mode...")
    
    # Mock account info
    account_info = {
        'login': ********,
        'balance': 10000.0,
        'equity': 10000.0,
        'currency': 'USD',
        'server': 'FBS-Demo'
    }
    
    print("✓ Simulation account info:")
    for key, value in account_info.items():
        print(f"   {key}: {value}")
    
    # Mock price data
    mock_prices = {
        'XAUUSD': {'bid': 2000.50, 'ask': 2000.70},
        'EURUSD': {'bid': 1.0850, 'ask': 1.0852},
        'GBPUSD': {'bid': 1.2650, 'ask': 1.2652}
    }
    
    print("\n✓ Simulation price data:")
    for symbol, prices in mock_prices.items():
        print(f"   {symbol}: Bid={prices['bid']:.5f}, Ask={prices['ask']:.5f}")
    
    return True

def main():
    """Main test function"""
    print("Starting MT4 connection test...\n")
    
    # Test file communication
    mt4_working = test_mt4_files()
    
    # Test simulation mode
    sim_working = test_account_simulation()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if mt4_working:
        print("🎉 MT4 CONNECTION: WORKING!")
        print("✓ Your Expert Advisor is running")
        print("✓ File communication is working")
        print("✓ Ready for live trading")
        print("\nNext step: Run the trading bot!")
        print("Command: env\\Scripts\\python.exe run_bot.py")
        
    elif sim_working:
        print("⚠ MT4 CONNECTION: SIMULATION MODE")
        print("✓ Python environment is working")
        print("✓ Simulation mode is functional")
        print("⚠ MT4 Expert Advisor not connected")
        print("\nTo enable live trading:")
        print("1. Make sure MetaTrader 4 is running")
        print("2. Attach PythonBridge EA to any chart")
        print("3. Look for smiley face 😊 on the chart")
        print("4. Run this test again")
        
    else:
        print("❌ TESTS FAILED")
        print("Please check:")
        print("1. Python environment setup")
        print("2. File permissions")
        print("3. MetaTrader 4 installation")
    
    print("=" * 60)
    return mt4_working or sim_working

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
