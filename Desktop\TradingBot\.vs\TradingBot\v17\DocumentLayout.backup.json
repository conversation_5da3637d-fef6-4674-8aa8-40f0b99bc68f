{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\TradingBot\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\advanced_trading_bot.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:TradingBot\\advanced_trading_bot.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\scheduler.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\utils\\scheduler.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\logger.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\utils\\logger.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\helpers.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\utils\\helpers.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\utils\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\scripts\\install_dependencies.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\docs\\scripts\\install_dependencies.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\scripts\\setup_mt4.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\docs\\scripts\\setup_mt4.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\api.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\docs\\api.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\setup.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\docs\\setup.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\README.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\news_based.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\strategies\\news_based.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\trend_following.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\strategies\\trend_following.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\mean_reversion.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\strategies\\mean_reversion.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\base_strategy.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\strategies\\base_strategy.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\strategies\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\news.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\data\\news.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\realtime.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\data\\realtime.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\historical.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\data\\historical.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\data\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\core\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\trading_engine.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\core\\trading_engine.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\portfolio_manager.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\core\\portfolio_manager.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\mt4.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\brokers\\mt4.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\brokers\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\technical.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\analysis\\technical.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\sentiment.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\analysis\\sentiment.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\fundamental.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\analysis\\fundamental.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\market_analyzer.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\analysis\\market_analyzer.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\analysis\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\order_manager.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\core\\order_manager.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\risk_manager.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\core\\risk_manager.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\broker_interface.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\brokers\\broker_interface.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\test\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\test\\__init__.py||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\main.py||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{24C80D00-F506-4F34-8EC1-40E7F0E1D7D6}|TradingBot\\TradingBot.pyproj|solutionrelative:TradingBot\\src\\main.py||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "advanced_trading_bot.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\advanced_trading_bot.py", "RelativeDocumentMoniker": "TradingBot\\advanced_trading_bot.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\advanced_trading_bot.py", "RelativeToolTip": "TradingBot\\advanced_trading_bot.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-17T08:04:32.587Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "scheduler.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\scheduler.py", "RelativeDocumentMoniker": "TradingBot\\src\\utils\\scheduler.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\scheduler.py", "RelativeToolTip": "TradingBot\\src\\utils\\scheduler.py", "ViewState": "AgIAAEIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-16T21:01:22.18Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "install_dependencies.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\scripts\\install_dependencies.py", "RelativeDocumentMoniker": "TradingBot\\docs\\scripts\\install_dependencies.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\scripts\\install_dependencies.py", "RelativeToolTip": "TradingBot\\docs\\scripts\\install_dependencies.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAAaAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-16T14:26:30.476Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "setup_mt4.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\scripts\\setup_mt4.py", "RelativeDocumentMoniker": "TradingBot\\docs\\scripts\\setup_mt4.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\scripts\\setup_mt4.py", "RelativeToolTip": "TradingBot\\docs\\scripts\\setup_mt4.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAACkAAAANAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-16T14:26:46.952Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "setup.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\setup.md", "RelativeDocumentMoniker": "TradingBot\\docs\\setup.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\setup.md", "RelativeToolTip": "TradingBot\\docs\\setup.md", "ViewState": "AgIAAAoAAAAAAAAAAAAmwCoAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-16T14:28:03.312Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "api.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\api.md", "RelativeDocumentMoniker": "TradingBot\\docs\\api.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\docs\\api.md", "RelativeToolTip": "TradingBot\\docs\\api.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-16T14:28:17.035Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "README.md", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\README.md", "RelativeDocumentMoniker": "TradingBot\\README.md", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\README.md", "RelativeToolTip": "TradingBot\\README.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-16T14:28:48.811Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "logger.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\logger.py", "RelativeDocumentMoniker": "TradingBot\\src\\utils\\logger.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\logger.py", "RelativeToolTip": "TradingBot\\src\\utils\\logger.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAARAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-16T14:33:40.124Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "helpers.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\helpers.py", "RelativeDocumentMoniker": "TradingBot\\src\\utils\\helpers.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\helpers.py", "RelativeToolTip": "TradingBot\\src\\utils\\helpers.py", "ViewState": "AgIAADAAAAAAAAAAAAAAADQAAABAAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-16T14:33:51.318Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "__init__.py (utils)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\src\\utils\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\utils\\__init__.py", "RelativeToolTip": "TradingBot\\src\\utils\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAABAAAAAAAAAA==", "Icon": "00000000-0000-0000-0000-000000000000.000000|iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAMtSURBVDhPrZS9T1tnFMZ/1/cLGxy7BNuJIeCG2hIiSSWqRmmR8Fh16lhVVVTRIR34Ezo2kSqUqUJeKkHLEqlDWZAyUJZEoUNaoTatXEgsQykGFFyw8de997337RA5MeYqUx/pDOc55zx6znmlF/5PSCmVzjybzX4/NTV1U9f1TvoUKpWKrFard5aXl+/u7e1Vu+taNzE9PU0ikeimX2J1dVUWi8UvVFWVKysr3xQKhXJn/YwgQKFQ6KZQVZVUKoWu64FsNhtNp9O3LMsKapo2u7Gxcdju8xVMJpPdFIry6jqGYeiTk5MDuq5/IqW0hBBfFwqFOkCgc6gNwzDORPuuqqqytLTE3Nycura2diEUCk2Yphlvz/o63Nzc7KbQNI10Os27N95n4vp7aAGFZv3Emp+f//tUX2fSxtjYWDdFzRL8lD9kv9qk2bTRA3AxrGoXh4b7A4FXi/oK+jm0+pI8269xUKnz+Ok+wrGJ9Rn6R+NDI/F4vK/d53vDVCp1JioNwaONPfI7ZTzh4AmXaq2hNEQgGA6HQ+1ZX0EA13VxUfjrucO9xwf8tl3m2qUoE6l+zvfqCOEghCAWrV9d+u7mz17zjyee5w37rry1tQXAbkvnxycn7JZPEMLGdRyE4+A6Np7r4CkKhvIcmuvQWh8n9uWsr8NMJkMmk6Ehezg4riGEg+cINDwiQY1ISCca0nk7FeJasgpuE6x/wN75wNdhPp8HYGe3iWXZeEIw3K8xk20SVI+R0gPpETWOGAwegGi8EHWPI691ODKYwAiAEA63sgbvXPiV8dgzrsQLXBnIM9RbRBH/gqiBWwc9eeQraNs2tm0z8obJ5YEgYVMlds5DkS54NggLPA9cQAZA6tBz1cUY/NZ35VKpBIAJfJ69TMWCRGQLWja4LVAioARBV8DMHGG++Sc9bz1dXFyc9RUcHR09lSuKgumVJLW6gtsC9xB6b4BUQD2/88P93a8ePLj3KJfLNc4ILiwsvPwIOvHZpx9uXzIHU9i/v3iAk1+QfdelG5woP3x4u5jL5RrdM6+FlPKc9Jy7srW9J2vrnrRK21LK+1LKj2dmZlLtvv8AoKdgTW7XN1wAAAAASUVORK5CYII=", "WhenOpened": "2025-07-16T14:33:19.034Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "news_based.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\news_based.py", "RelativeDocumentMoniker": "TradingBot\\src\\strategies\\news_based.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\news_based.py", "RelativeToolTip": "TradingBot\\src\\strategies\\news_based.py", "ViewState": "AgIAACsAAAAAAAAAAAAiwDwAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:33:02.236Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "trend_following.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\trend_following.py", "RelativeDocumentMoniker": "TradingBot\\src\\strategies\\trend_following.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\trend_following.py", "RelativeToolTip": "TradingBot\\src\\strategies\\trend_following.py", "ViewState": "AgIAABsAAAAAAAAAAAAiwEEAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:32:48.754Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "mean_reversion.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\mean_reversion.py", "RelativeDocumentMoniker": "TradingBot\\src\\strategies\\mean_reversion.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\mean_reversion.py", "RelativeToolTip": "TradingBot\\src\\strategies\\mean_reversion.py", "ViewState": "AgIAABwAAAAAAAAAAAAiwEIAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:32:35.782Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "base_strategy.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\base_strategy.py", "RelativeDocumentMoniker": "TradingBot\\src\\strategies\\base_strategy.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\base_strategy.py", "RelativeToolTip": "TradingBot\\src\\strategies\\base_strategy.py", "ViewState": "AgIAADEAAAAAAAAAAAAiwFcAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:32:23.134Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "__init__.py (strategies)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\src\\strategies\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\strategies\\__init__.py", "RelativeToolTip": "TradingBot\\src\\strategies\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:32:11.539Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "news.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\news.py", "RelativeDocumentMoniker": "TradingBot\\src\\data\\news.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\news.py", "RelativeToolTip": "TradingBot\\src\\data\\news.py", "ViewState": "AgIAADgAAAAAAAAAAAAiwF4AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:31:44.095Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "realtime.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\realtime.py", "RelativeDocumentMoniker": "TradingBot\\src\\data\\realtime.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\realtime.py", "RelativeToolTip": "TradingBot\\src\\data\\realtime.py", "ViewState": "AgIAAEMAAAAAAAAAAAAiwGkAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:31:33.588Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "historical.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\historical.py", "RelativeDocumentMoniker": "TradingBot\\src\\data\\historical.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\historical.py", "RelativeToolTip": "TradingBot\\src\\data\\historical.py", "ViewState": "AgIAAE4AAAAAAAAAAAAiwHQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:31:20.918Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "__init__.py (data)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\src\\data\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\data\\__init__.py", "RelativeToolTip": "TradingBot\\src\\data\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:31:02.457Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "__init__.py (core)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\src\\core\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\__init__.py", "RelativeToolTip": "TradingBot\\src\\core\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:21:49.517Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "trading_engine.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\trading_engine.py", "RelativeDocumentMoniker": "TradingBot\\src\\core\\trading_engine.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\trading_engine.py", "RelativeToolTip": "TradingBot\\src\\core\\trading_engine.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAFkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:22:02.245Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "portfolio_manager.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\portfolio_manager.py", "RelativeDocumentMoniker": "TradingBot\\src\\core\\portfolio_manager.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\portfolio_manager.py", "RelativeToolTip": "TradingBot\\src\\core\\portfolio_manager.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAKsAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:22:35.537Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "mt4.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\mt4.py", "RelativeDocumentMoniker": "TradingBot\\src\\brokers\\mt4.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\mt4.py", "RelativeToolTip": "TradingBot\\src\\brokers\\mt4.py", "ViewState": "AgIAAAMAAAAAAAAAAAAAACIAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:18:10.168Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "__init__.py (brokers)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\src\\brokers\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\__init__.py", "RelativeToolTip": "TradingBot\\src\\brokers\\__init__.py", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:17:53.258Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "technical.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\technical.py", "RelativeDocumentMoniker": "TradingBot\\src\\analysis\\technical.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\technical.py", "RelativeToolTip": "TradingBot\\src\\analysis\\technical.py", "ViewState": "AgIAAI4AAAAAAAAAAAAiwLQAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:19:39.626Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "sentiment.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\sentiment.py", "RelativeDocumentMoniker": "TradingBot\\src\\analysis\\sentiment.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\sentiment.py", "RelativeToolTip": "TradingBot\\src\\analysis\\sentiment.py", "ViewState": "AgIAAIIAAAAAAAAAAAAiwKgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:20:12.049Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "fundamental.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\fundamental.py", "RelativeDocumentMoniker": "TradingBot\\src\\analysis\\fundamental.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\fundamental.py", "RelativeToolTip": "TradingBot\\src\\analysis\\fundamental.py", "ViewState": "AgIAALAAAAAAAAAAAAAiwMQAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:19:53.309Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "market_analyzer.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\market_analyzer.py", "RelativeDocumentMoniker": "TradingBot\\src\\analysis\\market_analyzer.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\market_analyzer.py", "RelativeToolTip": "TradingBot\\src\\analysis\\market_analyzer.py", "ViewState": "AgIAAGUAAAAAAAAAAAAiwIIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:20:24.339Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "__init__.py (analysis)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\src\\analysis\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\analysis\\__init__.py", "RelativeToolTip": "TradingBot\\src\\analysis\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:19:26.987Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "order_manager.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\order_manager.py", "RelativeDocumentMoniker": "TradingBot\\src\\core\\order_manager.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\order_manager.py", "RelativeToolTip": "TradingBot\\src\\core\\order_manager.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:22:24.517Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "risk_manager.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\risk_manager.py", "RelativeDocumentMoniker": "TradingBot\\src\\core\\risk_manager.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\core\\risk_manager.py", "RelativeToolTip": "TradingBot\\src\\core\\risk_manager.py", "ViewState": "AgIAAHkAAAAAAAAAAAAiwJwAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:22:13.097Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "broker_interface.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\broker_interface.py", "RelativeDocumentMoniker": "TradingBot\\src\\brokers\\broker_interface.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\brokers\\broker_interface.py", "RelativeToolTip": "TradingBot\\src\\brokers\\broker_interface.py", "ViewState": "AgIAAM8AAAAAAAAAAAAiwPUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:18:32.208Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "__init__.py (test)", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\test\\__init__.py", "RelativeDocumentMoniker": "TradingBot\\test\\__init__.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\test\\__init__.py", "RelativeToolTip": "TradingBot\\test\\__init__.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:25:21.672Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "main.py", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\main.py", "RelativeDocumentMoniker": "TradingBot\\src\\main.py", "ToolTip": "C:\\Users\\<USER>\\Desktop\\TradingBot\\TradingBot\\src\\main.py", "RelativeToolTip": "TradingBot\\src\\main.py", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002457|", "WhenOpened": "2025-07-16T14:16:34.846Z"}]}]}]}