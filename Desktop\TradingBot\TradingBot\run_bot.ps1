# PowerShell script to run the trading bot
Write-Host "=" -ForegroundColor Green -NoNewline
Write-Host "=" * 59 -ForegroundColor Green
Write-Host "TRADING BOT LAUNCHER" -ForegroundColor Cyan
Write-Host "=" -ForegroundColor Green -NoNewline
Write-Host "=" * 59 -ForegroundColor Green

# Set location
Set-Location $PSScriptRoot

Write-Host ""
Write-Host "Step 1: Testing Python environment..." -ForegroundColor Yellow

try {
    $pythonPath = "TradingBot\env\Scripts\python.exe"
    $version = & $pythonPath --version 2>&1
    Write-Host "✓ Python version: $version" -ForegroundColor Green
} catch {
    Write-Host "✗ Python environment test failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 2: Testing basic imports..." -ForegroundColor Yellow

try {
    & $pythonPath -c "import pandas, numpy, MetaTrader5; print('✓ Basic imports successful')" 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Import test failed"
    }
} catch {
    Write-Host "✗ Import test failed: $_" -ForegroundColor Red
    Write-Host "This might be normal if MetaTrader 5 is not installed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Step 3: Checking configuration..." -ForegroundColor Yellow

if (Test-Path "TradingBot\config\config.json") {
    Write-Host "✓ Configuration file found" -ForegroundColor Green
} else {
    Write-Host "✗ Configuration file missing!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 4: Running the trading bot..." -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the bot when needed" -ForegroundColor Cyan
Write-Host ""

try {
    & $pythonPath run_bot.py
} catch {
    Write-Host "✗ Bot execution failed: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=" -ForegroundColor Green -NoNewline
Write-Host "=" * 59 -ForegroundColor Green
Write-Host "Bot execution completed" -ForegroundColor Cyan
Write-Host "=" -ForegroundColor Green -NoNewline
Write-Host "=" * 59 -ForegroundColor Green

Read-Host "Press Enter to exit"
