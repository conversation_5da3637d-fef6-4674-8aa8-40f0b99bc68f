#!/usr/bin/env python3
"""
Automated Trading Bot - Main Entry Point

A Python-based trading bot for MetaTrader 4 that trades stocks, gold, NASDAQ, and Dow Jones.
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any

# Add src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import setup_logging, get_logger
from core.trading_engine import TradingEngine


def load_config() -> Dict[str, Any]:
    """Load configuration from config files"""
    config_dir = Path(__file__).parent.parent / "config"

    # Default configuration
    default_config = {
        "mt4": {
            "server": "FBS-Demo",
            "login": 11653999,
            "password": "kTYg553z",
            "timeout": 60000,
            "portable": False,
            "deviation": 20,
            "magic_number": 234000
        },
        "risk": {
            "max_risk_per_trade": 0.02,  # 2% per trade
            "max_daily_loss": 0.05,      # 5% daily loss limit
            "max_open_positions": 5,
            "position_sizing": "fixed",   # or "percentage"
            "fixed_lot_size": 0.1
        },
        "analysis": {
            "timeframes": ["M15", "H1", "H4"],
            "indicators": ["RSI", "MACD", "EMA", "SMA"],
            "sentiment_weight": 0.3,
            "technical_weight": 0.7
        },
        "instruments": [
            "XAUUSD",    # Gold
            "NAS100",    # NASDAQ
            "US30",      # Dow Jones
            "EURUSD",    # EUR/USD
            "GBPUSD"     # GBP/USD
        ],
        "scan_interval": 60,  # seconds
        "trading_hours": {
            "start": "00:00",
            "end": "23:59",
            "timezone": "UTC"
        }
    }

    # Try to load from config file
    config_file = config_dir / "config.json"
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                file_config = json.load(f)
                # Merge with defaults
                default_config.update(file_config)
                logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            logger.warning(f"Failed to load config file: {e}. Using defaults.")
    else:
        # Create config directory and file
        config_dir.mkdir(exist_ok=True)
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=4)
        logger.info(f"Created default configuration at {config_file}")

    return default_config


def main():
    """Main entry point for the trading bot"""
    try:
        # Setup logging
        setup_logging()
        logger = get_logger(__name__)

        logger.info("=" * 50)
        logger.info("Starting Automated Trading Bot")
        logger.info("=" * 50)

        # Load configuration
        config = load_config()
        logger.info("Configuration loaded successfully")

        # Display MT4 connection info (without password)
        mt4_config = config['mt4'].copy()
        mt4_config['password'] = '***'
        logger.info(f"MT4 Configuration: {mt4_config}")

        # Initialize trading engine
        logger.info("Initializing trading engine...")
        engine = TradingEngine(config)

        # Start trading
        logger.info("Starting trading engine...")
        engine.run()

    except KeyboardInterrupt:
        logger.info("Trading bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        # Cleanup
        try:
            if 'engine' in locals():
                engine.shutdown()
        except:
            pass
        logger.info("Trading bot shutdown complete")


if __name__ == "__main__":
    main()