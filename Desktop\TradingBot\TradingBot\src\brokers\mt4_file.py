# src/brokers/mt4_file.py
"""
MetaTrader 4 File-based connection
This connects to MT4 via file communication with an Expert Advisor
"""

import os
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from .broker_interface import IBroker
from ..utils.logger import get_logger


class TradingError(Exception):
    """Custom exception for trading errors"""
    pass


class MT4FileBroker(IBroker):
    """MetaTrader 4 broker implementation using file communication"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(__name__)
        self.connected = False
        
        # File paths (MT4 Files folder)
        mt4_files_path = os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal")
        
        # Find the terminal folder (there might be multiple)
        self.mt4_data_path = None
        if os.path.exists(mt4_files_path):
            for folder in os.listdir(mt4_files_path):
                terminal_path = os.path.join(mt4_files_path, folder, "MQL4", "Files")
                if os.path.exists(terminal_path):
                    self.mt4_data_path = terminal_path
                    break
        
        if not self.mt4_data_path:
            # Fallback to current directory
            self.mt4_data_path = os.path.join(os.getcwd(), "mt4_files")
            os.makedirs(self.mt4_data_path, exist_ok=True)
            self.logger.warning(f"MT4 data path not found, using fallback: {self.mt4_data_path}")
        
        self.commands_file = os.path.join(self.mt4_data_path, "python_commands.txt")
        self.responses_file = os.path.join(self.mt4_data_path, "python_responses.txt")
        self.status_file = os.path.join(self.mt4_data_path, "mt4_status.txt")
        
        self.logger.info(f"MT4 communication path: {self.mt4_data_path}")
        
        # Try to connect
        if not self.connect():
            self.logger.warning("Could not connect to MT4 via files. Running in simulation mode.")
    
    def connect(self) -> bool:
        """Connect to MetaTrader 4 via file communication"""
        try:
            # Check if MT4 status file exists and is recent
            if os.path.exists(self.status_file):
                # Check if file is recent (within last 30 seconds)
                file_time = os.path.getmtime(self.status_file)
                if time.time() - file_time < 30:
                    # Read status
                    with open(self.status_file, 'r') as f:
                        status = f.read()
                    
                    if "MT4_READY" in status:
                        self.connected = True
                        self.logger.info("Successfully connected to MT4 via file communication")
                        return True
            
            self.logger.warning("MT4 not ready or Expert Advisor not running")
            return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to MT4: {e}")
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from MetaTrader 4"""
        try:
            # Clean up any remaining files
            for file_path in [self.commands_file, self.responses_file]:
                if os.path.exists(file_path):
                    os.remove(file_path)
            
            self.connected = False
            self.logger.info("Disconnected from MT4")
            return True
        except Exception as e:
            self.logger.error(f"Error disconnecting from MT4: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Check if connection to MT4 is active"""
        if not self.connected:
            return False
        
        try:
            # Check if status file is recent
            if os.path.exists(self.status_file):
                file_time = os.path.getmtime(self.status_file)
                if time.time() - file_time < 30:  # File updated within 30 seconds
                    return True
            
            # Try to reconnect
            return self.connect()
            
        except:
            return False
    
    def _send_command(self, command: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send command to MT4 and get response"""
        try:
            if not self.is_connected():
                return None
            
            # Write command to file
            with open(self.commands_file, 'w') as f:
                json.dump(command, f)
            
            # Wait for response (max 10 seconds)
            start_time = time.time()
            while time.time() - start_time < 10:
                if os.path.exists(self.responses_file):
                    # Wait a bit more to ensure file is completely written
                    time.sleep(0.1)
                    
                    try:
                        with open(self.responses_file, 'r') as f:
                            response_text = f.read().strip()
                        
                        # Delete response file
                        os.remove(self.responses_file)
                        
                        # Parse JSON response
                        return json.loads(response_text)
                        
                    except (json.JSONDecodeError, IOError):
                        # File might not be ready yet
                        time.sleep(0.1)
                        continue
                
                time.sleep(0.1)
            
            self.logger.warning("Timeout waiting for MT4 response")
            return None
            
        except Exception as e:
            self.logger.error(f"Error sending command to MT4: {e}")
            return None
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        try:
            if not self.is_connected():
                # Return mock data for simulation
                return {
                    'login': self.config['login'],
                    'balance': 10000.0,
                    'equity': 10000.0,
                    'margin': 0.0,
                    'margin_free': 10000.0,
                    'margin_level': 0.0,
                    'currency': 'USD',
                    'server': self.config['server'],
                    'leverage': 100
                }
            
            cmd = {'command': 'account_info'}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                return response['data']
            else:
                raise TradingError(f"Failed to get account info: {response}")
                
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            # Return mock data as fallback
            return {
                'login': self.config['login'],
                'balance': 10000.0,
                'equity': 10000.0,
                'margin': 0.0,
                'margin_free': 10000.0,
                'margin_level': 0.0,
                'currency': 'USD',
                'server': self.config['server'],
                'leverage': 100
            }
    
    def get_current_price(self, symbol: str) -> Dict[str, float]:
        """Get current bid/ask prices for a symbol"""
        try:
            if not self.is_connected():
                # Return mock prices for simulation
                mock_prices = {
                    'XAUUSD': {'bid': 2000.50, 'ask': 2000.70},
                    'EURUSD': {'bid': 1.0850, 'ask': 1.0852},
                    'GBPUSD': {'bid': 1.2650, 'ask': 1.2652},
                    'NAS100': {'bid': 15000.0, 'ask': 15001.0},
                    'US30': {'bid': 35000.0, 'ask': 35001.0}
                }
                
                price = mock_prices.get(symbol, {'bid': 1.0000, 'ask': 1.0001})
                price['time'] = time.time()
                return price
            
            cmd = {'command': 'get_price', 'symbol': symbol}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                return response['data']
            else:
                raise TradingError(f"Failed to get price for {symbol}: {response}")
                
        except Exception as e:
            self.logger.error(f"Error getting price for {symbol}: {e}")
            # Return mock price as fallback
            return {'bid': 1.0000, 'ask': 1.0001, 'time': time.time()}
    
    def place_order(self, symbol: str, order_type: str, volume: float, 
                   stop_loss: Optional[float] = None, take_profit: Optional[float] = None, 
                   comment: str = "", **kwargs) -> Dict[str, Any]:
        """Place a new order"""
        try:
            cmd = {
                'command': 'place_order',
                'symbol': symbol,
                'order_type': order_type,
                'volume': volume,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'comment': comment,
                'magic': self.config.get('magic_number', 234000),
                'deviation': self.config.get('deviation', 20)
            }
            
            if not self.is_connected():
                # Simulate order placement
                self.logger.info(f"SIMULATION: {order_type} order for {symbol}, volume: {volume}")
                return {
                    'ticket': int(time.time()),  # Mock ticket
                    'retcode': 0,
                    'deal': int(time.time()),
                    'order': int(time.time()),
                    'volume': volume,
                    'price': self.get_current_price(symbol)['ask' if order_type == 'BUY' else 'bid'],
                    'comment': comment
                }
            
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                self.logger.info(f"Order placed successfully: {symbol} {order_type} {volume} lots")
                return response['data']
            else:
                raise TradingError(f"Order failed: {response}")
                
        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            raise TradingError(f"Failed to place order: {e}")
    
    # Implement remaining required methods with simulation fallbacks
    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        return None
    
    def get_all_positions(self) -> List[Dict[str, Any]]:
        return []
    
    def get_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        return []
    
    def close_position(self, ticket: int) -> bool:
        if not self.is_connected():
            self.logger.info(f"SIMULATION: Closing position {ticket}")
            return True
        
        cmd = {'command': 'close_position', 'ticket': ticket}
        response = self._send_command(cmd)
        return response and response.get('success', False)
    
    def modify_position(self, ticket: int, stop_loss: Optional[float] = None, 
                       take_profit: Optional[float] = None) -> bool:
        return True
    
    def cancel_order(self, ticket: int) -> bool:
        return True
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        return {
            'name': symbol,
            'digits': 5 if 'JPY' not in symbol else 3,
            'point': 0.00001 if 'JPY' not in symbol else 0.001,
            'spread': 2,
            'volume_min': 0.01,
            'volume_max': 100.0,
            'volume_step': 0.01,
            'contract_size': 100000,
            'margin_initial': 0.01,
            'currency_base': symbol[:3],
            'currency_profit': symbol[3:],
            'currency_margin': 'USD'
        }
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                           start: Optional[datetime] = None, end: Optional[datetime] = None, 
                           count: Optional[int] = None) -> List[Dict[str, Any]]:
        # Generate mock historical data for simulation
        import random
        data = []
        base_price = 1.0000
        
        if symbol == 'XAUUSD':
            base_price = 2000.0
        elif symbol in ['NAS100', 'US30']:
            base_price = 15000.0
        
        for i in range(count or 100):
            price = base_price + random.uniform(-50, 50)
            data.append({
                'time': datetime.now(),
                'open': price,
                'high': price + random.uniform(0, 10),
                'low': price - random.uniform(0, 10),
                'close': price + random.uniform(-5, 5),
                'volume': random.randint(100, 1000)
            })
        
        return data
    
    def get_market_depth(self, symbol: str) -> Dict[str, List[Dict[str, float]]]:
        return {'bids': [], 'asks': []}
    
    def get_server_time(self) -> datetime:
        return datetime.now()
    
    def get_margin_requirements(self, symbol: str, volume: float) -> Dict[str, float]:
        return {'margin_required': volume * 1000, 'margin_currency': 'USD'}
    
    def get_available_symbols(self) -> List[str]:
        return ['XAUUSD', 'EURUSD', 'GBPUSD', 'NAS100', 'US30']
    
    def get_trade_history(self, start: datetime, end: datetime, 
                         symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        return []
