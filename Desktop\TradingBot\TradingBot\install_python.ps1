# PowerShell script to install Python
Write-Host "=" -ForegroundColor Green -NoNewline
Write-Host "=" * 49 -ForegroundColor Green
Write-Host "Python Installation Script" -ForegroundColor Cyan
Write-Host "=" -ForegroundColor Green -NoNewline
Write-Host "=" * 49 -ForegroundColor Green

# Check if Python is already installed
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "Python is already installed: $pythonVersion" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Python not found in PATH" -ForegroundColor Yellow
}

# Try to install using winget
Write-Host "Attempting to install Python using Windows Package Manager..." -ForegroundColor Yellow

try {
    # Check if winget is available
    winget --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Installing Python 3.11..." -ForegroundColor Yellow
        winget install Python.Python.3.11
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Python installed successfully!" -ForegroundColor Green
            Write-Host "Please restart your PowerShell/Command Prompt and run this script again." -ForegroundColor Cyan
            exit 0
        }
    }
} catch {
    Write-Host "winget not available or failed" -ForegroundColor Red
}

# If winget fails, provide manual instructions
Write-Host ""
Write-Host "Automatic installation failed. Please install Python manually:" -ForegroundColor Yellow
Write-Host ""
Write-Host "Option 1 - Microsoft Store (Recommended):" -ForegroundColor Cyan
Write-Host "1. Open Microsoft Store" -ForegroundColor White
Write-Host "2. Search for 'Python 3.11'" -ForegroundColor White
Write-Host "3. Install the official Python package" -ForegroundColor White
Write-Host ""
Write-Host "Option 2 - Python.org:" -ForegroundColor Cyan
Write-Host "1. Go to https://python.org/downloads/" -ForegroundColor White
Write-Host "2. Download Python 3.11 or 3.12" -ForegroundColor White
Write-Host "3. During installation, CHECK 'Add Python to PATH'" -ForegroundColor Red
Write-Host ""
Write-Host "After installation, restart PowerShell and run:" -ForegroundColor Yellow
Write-Host "python --version" -ForegroundColor Green
Write-Host ""
