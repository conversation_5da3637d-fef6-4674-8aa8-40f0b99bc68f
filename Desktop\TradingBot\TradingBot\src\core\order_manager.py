
# src/core/order_manager.py
from typing import Dict, Optional
from ..brokers import IBroker

class OrderManager:
    def __init__(self, broker: IBroker):
        """
        Initialize order manager with broker connection
        
        Args:
            broker: Initialized broker instance implementing IBroker interface
        """
        self.broker = broker
        self.pending_orders = {}
        self.order_history = []

    def place_order(
        self,
        symbol: str,
        order_type: str,
        volume: float,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        comment: str = "",
        **kwargs
    ) -> Dict:
        """
        Place a new order with the broker
        
        Args:
            symbol: Trading symbol
            order_type: Order type (BUY/SELL)
            volume: Trade volume
            stop_loss: Optional stop loss price
            take_profit: Optional take profit price
            comment: Order comment
            **kwargs: Additional order parameters
            
        Returns:
            Dictionary with order execution results
        """
        try:
            result = self.broker.place_order(
                symbol=symbol,
                order_type=order_type,
                volume=volume,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=comment,
                **kwargs
            )
            
            if result['status']:
                self.order_history.append({
                    'time': datetime.now(),
                    'symbol': symbol,
                    'type': order_type,
                    'volume': volume,
                    'price': result['price'],
                    'sl': stop_loss,
                    'tp': take_profit,
                    'ticket': result['ticket']
                })
                
            return result
            
        except Exception as e:
            return {
                'status': False,
                'error': str(e),
                'symbol': symbol,
                'type': order_type
            }

    def modify_order(
        self,
        ticket: int,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        **kwargs
    ) -> bool:
        """
        Modify an existing order
        
        Args:
            ticket: Order ticket number
            stop_loss: New stop loss price
            take_profit: New take profit price
            **kwargs: Additional modification parameters
            
        Returns:
            True if modification was successful
        """
        try:
            return self.broker.modify_order(
                ticket=ticket,
                stop_loss=stop_loss,
                take_profit=take_profit,
                **kwargs
            )
        except Exception as e:
            print(f"Error modifying order {ticket}: {str(e)}")
            return False

    def close_position(self, symbol: str, reason: str = "") -> bool:
        """
        Close an open position
        
        Args:
            symbol: Trading symbol
            reason: Reason for closing
            
        Returns:
            True if position was closed successfully
        """
        try:
            result = self.broker.close_position(symbol)
            if result:
                self.order_history.append({
                    'time': datetime.now(),
                    'symbol': symbol,
                    'type': 'CLOSE',
                    'reason': reason
                })
            return result
        except Exception as e:
            print(f"Error closing position {symbol}: {str(e)}")
            return False

    def cancel_order(self, ticket: int) -> bool:
        """
        Cancel a pending order
        
        Args:
            ticket: Order ticket number
            
        Returns:
            True if order was canceled successfully
        """
        try:
            return self.broker.cancel_order(ticket)
        except Exception as e:
            print(f"Error canceling order {ticket}: {str(e)}")
            return False

    def get_order_status(self, ticket: int) -> Dict:
        """
        Get status of an order
        
        Args:
            ticket: Order ticket number
            
        Returns:
            Dictionary with order status information
        """
        orders = self.broker.get_orders()
        for order in orders:
            if order['ticket'] == ticket:
                return order
        return {'status': 'NOT_FOUND'}

    def bulk_cancel_orders(self, symbol: Optional[str] = None) -> int:
        """
        Cancel all pending orders, optionally filtered by symbol
        
        Args:
            symbol: Optional symbol filter
            
        Returns:
            Number of orders canceled
        """
        canceled = 0
        orders = self.broker.get_orders()
        
        for order in orders:
            if symbol is None or order['symbol'] == symbol:
                if self.cancel_order(order['ticket']):
                    canceled += 1
                    
        return canceled