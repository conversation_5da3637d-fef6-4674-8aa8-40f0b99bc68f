#!/usr/bin/env python3
"""
Direct bot runner - bypasses complex path issues
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the src directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'TradingBot', 'src')
sys.path.insert(0, src_dir)

def setup_basic_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def test_imports():
    """Test all required imports"""
    logger = setup_basic_logging()
    logger.info("Testing imports...")
    
    try:
        import pandas as pd
        import numpy as np
        logger.info("✓ pandas and numpy imported")
        
        import MetaTrader5 as mt5
        logger.info("✓ MetaTrader5 imported")
        
        # Test our custom modules
        from utils.logger import get_logger
        from brokers.mt4 import MT4Broker
        from analysis.simple_analyzer import SimpleMarketAnalyzer
        from core.risk_manager import RiskManager
        from core.trading_engine import TradingEngine
        
        logger.info("✓ All custom modules imported successfully")
        return True
        
    except Exception as e:
        logger.error(f"✗ Import error: {e}")
        return False

def load_config():
    """Load configuration"""
    config_path = os.path.join(current_dir, 'TradingBot', 'config', 'config.json')
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"Error loading config: {e}")
        return None

def test_mt4_connection():
    """Test MetaTrader 4 connection"""
    logger = logging.getLogger(__name__)

    try:
        from brokers.mt4_file import MT4FileBroker

        logger.info("Testing MT4 file communication...")

        # Load config for connection test
        config = load_config()
        if not config:
            logger.error("Cannot load configuration for MT4 test")
            return False

        # Test MT4 file broker
        broker = MT4FileBroker(config['mt4'])

        if broker.is_connected():
            logger.info("✓ Connected to MT4 via file communication successfully")

            # Test account info
            account_info = broker.get_account_info()
            logger.info(f"Account: {account_info['login']} on {account_info['server']}")
            logger.info(f"Balance: ${account_info['balance']:.2f}")

            broker.disconnect()
            return True
        else:
            logger.warning("MT4 socket connection not available")
            logger.info("Running in SIMULATION MODE")
            logger.info("To connect to real MT4:")
            logger.info("1. Install MetaTrader 4 from your broker (FBS)")
            logger.info("2. Follow the MT4_SETUP_GUIDE.md instructions")
            logger.info("3. Install and run the PythonBridge Expert Advisor")
            return False

    except Exception as e:
        logger.error(f"MT4 connection error: {e}")
        logger.info("Will run in simulation mode")
        return False

def run_trading_bot():
    """Run the trading bot"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("AUTOMATED TRADING BOT STARTING")
    logger.info("=" * 60)
    
    # Test imports
    if not test_imports():
        logger.error("Import test failed. Cannot continue.")
        return False
    
    # Load configuration
    config = load_config()
    if not config:
        logger.error("Failed to load configuration")
        return False

    logger.info(f"Configuration loaded - Server: {config['mt4']['server']}")
    logger.info(f"Account: {config['mt4']['login']}")
        logger.info("Password updated with correct credentials")
    
    # Test MT4 connection
    mt4_available = test_mt4_connection()

    if not mt4_available:
        logger.warning("MT4 not available - running in simulation mode")
        logger.info("The bot will still work, but trades will be simulated")
        # Continue anyway - simulation mode is functional
    
    try:
        # Import and run the trading engine
        from core.trading_engine import TradingEngine
        
        logger.info("Initializing trading engine...")
        engine = TradingEngine(config)
        
        logger.info("Starting trading engine...")
        logger.info("Press Ctrl+C to stop the bot")
        
        engine.run()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Trading engine error: {e}", exc_info=True)
        return False
    finally:
        try:
            if 'engine' in locals():
                engine.shutdown()
        except:
            pass
        logger.info("Bot shutdown complete")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("TRADING BOT LAUNCHER")
    print("=" * 60)
    print()
    
    success = run_trading_bot()
    
    if success:
        print("\n✓ Bot completed successfully!")
    else:
        print("\n✗ Bot encountered issues.")
        print("\nNext steps:")
        print("1. Install MetaTrader 5 if not already installed")
        print("2. Make sure MetaTrader 5 is running")
        print("3. Check your internet connection")
        print("4. Verify your broker credentials")
    
    input("\nPress Enter to exit...")
