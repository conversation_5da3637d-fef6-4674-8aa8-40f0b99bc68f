#!/usr/bin/env python3
"""
Monitoring & Alerts System
Web dashboard, email/SMS notifications, performance analytics, trade journal
"""

import smtplib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import sqlite3
import pandas as pd
from flask import Flask, render_template_string, jsonify
import threading
import time

logger = logging.getLogger(__name__)

class MonitoringSystem:
    """Comprehensive monitoring and alerting system"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.db_path = "trading_journal.db"
        self.init_database()
        
        # Email configuration
        self.email_config = config.get('email', {})
        self.sms_config = config.get('sms', {})
        
        # Alert thresholds
        self.alert_thresholds = {
            'max_drawdown': 0.05,  # 5%
            'daily_loss': 0.03,    # 3%
            'position_loss': 0.02,  # 2%
            'connection_timeout': 30  # seconds
        }
        
        # Performance tracking
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'win_rate': 0.0
        }
        
        # Web dashboard
        self.app = Flask(__name__)
        self.setup_web_routes()
        
    def init_database(self):
        """Initialize SQLite database for trade journal"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create trades table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    action TEXT NOT NULL,
                    size REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    exit_price REAL,
                    stop_loss REAL,
                    take_profit REAL,
                    pnl REAL,
                    duration_minutes INTEGER,
                    strategy TEXT,
                    confidence REAL,
                    market_conditions TEXT,
                    notes TEXT
                )
            ''')
            
            # Create performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_performance (
                    date TEXT PRIMARY KEY,
                    starting_balance REAL,
                    ending_balance REAL,
                    daily_pnl REAL,
                    trades_count INTEGER,
                    win_rate REAL,
                    max_drawdown REAL,
                    sharpe_ratio REAL
                )
            ''')
            
            # Create alerts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    message TEXT NOT NULL,
                    acknowledged BOOLEAN DEFAULT FALSE
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    def log_trade(self, trade_data: Dict):
        """Log trade to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO trades (
                    timestamp, symbol, action, size, entry_price, exit_price,
                    stop_loss, take_profit, pnl, duration_minutes, strategy,
                    confidence, market_conditions, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get('timestamp', datetime.now().isoformat()),
                trade_data.get('symbol'),
                trade_data.get('action'),
                trade_data.get('size'),
                trade_data.get('entry_price'),
                trade_data.get('exit_price'),
                trade_data.get('stop_loss'),
                trade_data.get('take_profit'),
                trade_data.get('pnl'),
                trade_data.get('duration_minutes'),
                trade_data.get('strategy'),
                trade_data.get('confidence'),
                trade_data.get('market_conditions'),
                trade_data.get('notes')
            ))
            
            conn.commit()
            conn.close()
            
            # Update performance metrics
            self.update_performance_metrics()
            
        except Exception as e:
            logger.error(f"Error logging trade: {e}")
    
    def send_email_alert(self, subject: str, message: str, priority: str = "NORMAL"):
        """Send email alert"""
        try:
            if not self.email_config.get('enabled', False):
                return
            
            smtp_server = self.email_config.get('smtp_server', 'smtp.gmail.com')
            smtp_port = self.email_config.get('smtp_port', 587)
            email = self.email_config.get('email')
            password = self.email_config.get('password')
            to_email = self.email_config.get('to_email')
            
            if not all([email, password, to_email]):
                logger.warning("Email configuration incomplete")
                return
            
            # Create message
            msg = MimeMultipart()
            msg['From'] = email
            msg['To'] = to_email
            msg['Subject'] = f"[{priority}] Trading Bot Alert: {subject}"
            
            # Email body
            body = f"""
            Trading Bot Alert
            
            Priority: {priority}
            Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            
            Message:
            {message}
            
            ---
            Automated Trading Bot Monitoring System
            """
            
            msg.attach(MimeText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(email, password)
            text = msg.as_string()
            server.sendmail(email, to_email, text)
            server.quit()
            
            logger.info(f"Email alert sent: {subject}")
            
        except Exception as e:
            logger.error(f"Error sending email alert: {e}")
    
    def send_sms_alert(self, message: str):
        """Send SMS alert (placeholder - integrate with Twilio/similar)"""
        try:
            if not self.sms_config.get('enabled', False):
                return
            
            # This is a placeholder - integrate with actual SMS service
            logger.info(f"SMS Alert (simulated): {message}")
            
            # Example Twilio integration:
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # client.messages.create(
            #     body=message,
            #     from_='+**********',
            #     to='+**********'
            # )
            
        except Exception as e:
            logger.error(f"Error sending SMS alert: {e}")
    
    def create_alert(self, alert_type: str, severity: str, message: str):
        """Create and store alert"""
        try:
            # Store in database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts (timestamp, alert_type, severity, message)
                VALUES (?, ?, ?, ?)
            ''', (datetime.now().isoformat(), alert_type, severity, message))
            
            conn.commit()
            conn.close()
            
            # Send notifications based on severity
            if severity in ['HIGH', 'CRITICAL']:
                self.send_email_alert(f"{alert_type} Alert", message, severity)
                if severity == 'CRITICAL':
                    self.send_sms_alert(f"CRITICAL: {message}")
            
            logger.warning(f"[{severity}] {alert_type}: {message}")
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
    
    def check_alert_conditions(self, current_data: Dict):
        """Check for alert conditions"""
        try:
            # Check drawdown
            if current_data.get('drawdown', 0) > self.alert_thresholds['max_drawdown']:
                self.create_alert(
                    'DRAWDOWN',
                    'HIGH',
                    f"Maximum drawdown exceeded: {current_data['drawdown']:.2%}"
                )
            
            # Check daily loss
            if current_data.get('daily_pnl', 0) < -self.alert_thresholds['daily_loss'] * current_data.get('account_balance', 10000):
                self.create_alert(
                    'DAILY_LOSS',
                    'HIGH',
                    f"Daily loss limit approached: ${current_data.get('daily_pnl', 0):.2f}"
                )
            
            # Check position loss
            for position in current_data.get('positions', []):
                if position.get('unrealized_pnl', 0) < -self.alert_thresholds['position_loss'] * current_data.get('account_balance', 10000):
                    self.create_alert(
                        'POSITION_LOSS',
                        'MEDIUM',
                        f"Large position loss: {position['symbol']} ${position['unrealized_pnl']:.2f}"
                    )
            
            # Check connection status
            if not current_data.get('connected', True):
                self.create_alert(
                    'CONNECTION',
                    'CRITICAL',
                    "Lost connection to broker"
                )
            
        except Exception as e:
            logger.error(f"Error checking alert conditions: {e}")
    
    def update_performance_metrics(self):
        """Update performance metrics from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get trade statistics
            df = pd.read_sql_query("SELECT * FROM trades WHERE pnl IS NOT NULL", conn)
            
            if not df.empty:
                self.performance_metrics['total_trades'] = len(df)
                self.performance_metrics['winning_trades'] = len(df[df['pnl'] > 0])
                self.performance_metrics['losing_trades'] = len(df[df['pnl'] < 0])
                self.performance_metrics['total_pnl'] = df['pnl'].sum()
                self.performance_metrics['win_rate'] = self.performance_metrics['winning_trades'] / self.performance_metrics['total_trades']
                
                # Calculate Sharpe ratio (simplified)
                if len(df) > 10:
                    returns = df['pnl'].values
                    self.performance_metrics['sharpe_ratio'] = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def setup_web_routes(self):
        """Setup web dashboard routes"""
        
        @self.app.route('/')
        def dashboard():
            return render_template_string('''
            <!DOCTYPE html>
            <html>
            <head>
                <title>Trading Bot Dashboard</title>
                <meta http-equiv="refresh" content="30">
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .metric { display: inline-block; margin: 10px 20px; text-align: center; }
                    .metric-value { font-size: 24px; font-weight: bold; color: #2c3e50; }
                    .metric-label { font-size: 12px; color: #7f8c8d; }
                    .positive { color: #27ae60; }
                    .negative { color: #e74c3c; }
                    .alert { padding: 10px; margin: 5px 0; border-radius: 4px; }
                    .alert-high { background-color: #f8d7da; border: 1px solid #f5c6cb; }
                    .alert-medium { background-color: #fff3cd; border: 1px solid #ffeaa7; }
                    table { width: 100%; border-collapse: collapse; }
                    th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                    th { background-color: #f8f9fa; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🤖 Trading Bot Dashboard</h1>
                    
                    <div class="card">
                        <h2>Performance Metrics</h2>
                        <div class="metric">
                            <div class="metric-value" id="total-pnl">$0.00</div>
                            <div class="metric-label">Total P&L</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value" id="win-rate">0%</div>
                            <div class="metric-label">Win Rate</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value" id="total-trades">0</div>
                            <div class="metric-label">Total Trades</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value" id="sharpe-ratio">0.00</div>
                            <div class="metric-label">Sharpe Ratio</div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h2>Recent Alerts</h2>
                        <div id="alerts-container">
                            <p>No recent alerts</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h2>Recent Trades</h2>
                        <div id="trades-container">
                            <p>Loading trades...</p>
                        </div>
                    </div>
                </div>
                
                <script>
                    function updateDashboard() {
                        fetch('/api/metrics')
                            .then(response => response.json())
                            .then(data => {
                                document.getElementById('total-pnl').textContent = '$' + data.total_pnl.toFixed(2);
                                document.getElementById('win-rate').textContent = (data.win_rate * 100).toFixed(1) + '%';
                                document.getElementById('total-trades').textContent = data.total_trades;
                                document.getElementById('sharpe-ratio').textContent = data.sharpe_ratio.toFixed(2);
                            });
                        
                        fetch('/api/alerts')
                            .then(response => response.json())
                            .then(data => {
                                const container = document.getElementById('alerts-container');
                                if (data.length === 0) {
                                    container.innerHTML = '<p>No recent alerts</p>';
                                } else {
                                    container.innerHTML = data.map(alert => 
                                        `<div class="alert alert-${alert.severity.toLowerCase()}">
                                            <strong>${alert.alert_type}</strong> - ${alert.message}
                                            <small style="float: right;">${alert.timestamp}</small>
                                        </div>`
                                    ).join('');
                                }
                            });
                    }
                    
                    // Update every 30 seconds
                    setInterval(updateDashboard, 30000);
                    updateDashboard();
                </script>
            </body>
            </html>
            ''')
        
        @self.app.route('/api/metrics')
        def api_metrics():
            return jsonify(self.performance_metrics)
        
        @self.app.route('/api/alerts')
        def api_alerts():
            try:
                conn = sqlite3.connect(self.db_path)
                df = pd.read_sql_query(
                    "SELECT * FROM alerts ORDER BY timestamp DESC LIMIT 10", 
                    conn
                )
                conn.close()
                return jsonify(df.to_dict('records'))
            except:
                return jsonify([])
    
    def start_web_dashboard(self, port: int = 5000):
        """Start web dashboard in background thread"""
        def run_app():
            self.app.run(host='0.0.0.0', port=port, debug=False)
        
        dashboard_thread = threading.Thread(target=run_app, daemon=True)
        dashboard_thread.start()
        logger.info(f"Web dashboard started on http://localhost:{port}")
    
    def generate_daily_report(self) -> str:
        """Generate daily performance report"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            
            conn = sqlite3.connect(self.db_path)
            
            # Get today's trades
            today_trades = pd.read_sql_query(
                "SELECT * FROM trades WHERE DATE(timestamp) = ? AND pnl IS NOT NULL",
                conn, params=[today]
            )
            
            # Get recent alerts
            recent_alerts = pd.read_sql_query(
                "SELECT * FROM alerts WHERE DATE(timestamp) = ? ORDER BY timestamp DESC",
                conn, params=[today]
            )
            
            conn.close()
            
            # Generate report
            report = f"""
            📊 DAILY TRADING REPORT - {today}
            ================================
            
            📈 Performance Summary:
            • Total Trades: {len(today_trades)}
            • Winning Trades: {len(today_trades[today_trades['pnl'] > 0]) if not today_trades.empty else 0}
            • Daily P&L: ${today_trades['pnl'].sum():.2f if not today_trades.empty else 0}
            • Win Rate: {(len(today_trades[today_trades['pnl'] > 0]) / len(today_trades) * 100):.1f}% if not today_trades.empty else 0%
            
            🚨 Alerts Today: {len(recent_alerts)}
            
            📋 Trade Details:
            """
            
            if not today_trades.empty:
                for _, trade in today_trades.iterrows():
                    report += f"• {trade['symbol']} {trade['action']} - P&L: ${trade['pnl']:.2f}\n"
            else:
                report += "• No trades executed today\n"
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating daily report: {e}")
            return "Error generating report"
