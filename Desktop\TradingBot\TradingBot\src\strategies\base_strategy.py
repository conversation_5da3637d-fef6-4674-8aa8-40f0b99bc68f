# src/strategies/base_strategy.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import pandas as pd

class BaseStrategy(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__
        
    @abstractmethod
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze market data and generate signals
        Returns dictionary with analysis results
        """
        pass
        
    @abstractmethod
    def get_signal(self, analysis: Dict[str, Any]) -> str:
        """
        Generate trading signal from analysis
        Returns 'BUY', 'SELL', or 'HOLD'
        """
        pass
        
    def calculate_stop_loss(
        self,
        entry_price: float,
        signal: str,
        atr: Optional[float] = None
    ) -> float:
        """Calculate stop loss price"""
        if atr is not None:
            # Use ATR-based stop loss
            if signal == 'BUY':
                return entry_price - (atr * self.config.get('atr_multiplier', 2))
            else:
                return entry_price + (atr * self.config.get('atr_multiplier', 2))
        else:
            # Use percentage-based stop loss
            sl_pct = self.config.get('stop_loss_pct', 0.02)
            if signal == 'BUY':
                return entry_price * (1 - sl_pct)
            else:
                return entry_price * (1 + sl_pct)
                
    def calculate_take_profit(
        self,
        entry_price: float,
        signal: str,
        stop_loss: float,
        risk_reward: float = 2.0
    ) -> float:
        """Calculate take profit price"""
        if signal == 'BUY':
            distance = entry_price - stop_loss
            return entry_price + (distance * risk_reward)
        else:
            distance = stop_loss - entry_price
            return entry_price - (distance * risk_reward)
            
    def should_exit(
        self,
        position: Dict[str, Any],
        current_price: float,
        current_time: pd.Timestamp
    ) -> bool:
        """Determine if position should be exited"""
        # Check stop loss/take profit
        if position['type'] == 'BUY':
            if current_price <= position['stop_loss']:
                return True, 'stop_loss'
            if current_price >= position['take_profit']:
                return True, 'take_profit'
        else:
            if current_price >= position['stop_loss']:
                return True, 'stop_loss'
            if current_price <= position['take_profit']:
                return True, 'take_profit'
                
        # Check time-based exit
        if 'max_duration' in self.config:
            duration = current_time - position['entry_time']
            if duration.total_seconds() >= self.config['max_duration'] * 3600:
                return True, 'time_exit'
                
        return False, None
