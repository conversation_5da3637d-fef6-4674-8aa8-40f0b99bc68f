# src/analysis/market_analyzer.py
from typing import Dict, Any
import pandas as pd
from .technical import TechnicalAnalyzer
from .fundamental import FundamentalAnalyzer
from .sentiment import NewsSentimentAnalyzer

class MarketAnalyzer:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.technical_analyzer = TechnicalAnalyzer(config)
        self.fundamental_analyzer = FundamentalAnalyzer(config)
        self.sentiment_analyzer = NewsSentimentAnalyzer(config)
        
    def analyze_market(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform complete market analysis for a given symbol
        Returns combined analysis with recommendation
        """
        # Technical analysis
        technical = self.technical_analyzer.analyze(data)
        
        # Fundamental analysis
        fundamental = self.fundamental_analyzer.analyze(symbol)
        
        # News sentiment analysis
        sentiment = self.sentiment_analyzer.get_sentiment(symbol)
        
        # Combine analyses
        combined_score = self._combine_scores(
            technical.get('score', 0.5),
            fundamental.get('score', 0.5),
            sentiment.get('score', 0.5)
        )
        
        # Generate recommendation
        recommendation = self._generate_recommendation(combined_score, technical, fundamental, sentiment)
        
        return {
            'symbol': symbol,
            'timestamp': pd.Timestamp.now(),
            'technical': technical,
            'fundamental': fundamental,
            'sentiment': sentiment,
            'combined_score': combined_score,
            'recommendation': recommendation,
            'confidence': self._calculate_confidence(technical, fundamental, sentiment)
        }
    
    def _combine_scores(self, technical: float, fundamental: float, sentiment: float) -> float:
        """
        Combine scores from different analysis types with weights
        Customize these weights based on your strategy
        """
        weights = self.config.get('analysis_weights', {
            'technical': 0.6,
            'fundamental': 0.2,
            'sentiment': 0.2
        })
        
        return (
            weights['technical'] * technical +
            weights['fundamental'] * fundamental +
            weights['sentiment'] * sentiment
        )
    
    def _generate_recommendation(self, combined_score: float, 
                               technical: Dict[str, Any], 
                               fundamental: Dict[str, Any], 
                               sentiment: Dict[str, Any]) -> str:
        """
        Generate trading recommendation based on analysis
        """
        # Basic threshold-based recommendation
        if combined_score > 0.7:
            return 'STRONG_BUY'
        elif combined_score > 0.6:
            return 'BUY'
        elif combined_score < 0.3:
            return 'STRONG_SELL'
        elif combined_score < 0.4:
            return 'SELL'
        
        # Check for strong technical signals even if combined score is moderate
        tech_signals = technical.get('signals', {})
        if tech_signals.get('ema_cross') == 'golden' and tech_signals.get('macd') == 'bullish':
            return 'BUY'
        if tech_signals.get('ema_cross') == 'death' and tech_signals.get('macd') == 'bearish':
            return 'SELL'
        
        # Check for high-impact fundamental events
        fund_events = fundamental.get('economic_events', {})
        if fund_events.get('count', 0) > 0:
            next_event = fund_events.get('next_event', {})
            if next_event.get('expected_impact') == 'Positive':
                return 'BUY' if combined_score > 0.55 else 'HOLD'
            elif next_event.get('expected_impact') == 'Negative':
                return 'SELL' if combined_score < 0.45 else 'HOLD'
        
        # Default to hold
        return 'HOLD'
    
    def _calculate_confidence(self, 
                            technical: Dict[str, Any], 
                            fundamental: Dict[str, Any], 
                            sentiment: Dict[str, Any]) -> float:
        """
        Calculate confidence score (0-1) in the analysis
        """
        # Technical confidence based on indicator agreement
        tech_signals = technical.get('signals', {})
        tech_agreement = sum(1 for s in tech_signals.values() if s in ['bullish', 'oversold', 'golden'])
        tech_agreement -= sum(1 for s in tech_signals.values() if s in ['bearish', 'overbought', 'death'])
        tech_confidence = min(1, max(0, 0.5 + tech_agreement * 0.1))
        
        # Fundamental confidence based on data completeness
        fund_confidence = 0.5
        if fundamental.get('economic_events', {}).get('count', 0) > 0:
            fund_confidence += 0.2
        if fundamental.get('earnings', {}).get('components', 0) > 0:
            fund_confidence += 0.1
        
        # Sentiment confidence based on article count
        sentiment_conf = min(1, sentiment.get('articles_analyzed', 0) / 10 * 0.3 + 0.5)
        
        # Weighted average
        return (
            0.6 * tech_confidence +
            0.2 * fund_confidence +
            0.2 * sentiment_conf
        )