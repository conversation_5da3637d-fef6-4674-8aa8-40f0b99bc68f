#!/usr/bin/env python3
"""
Test real trading functionality
"""

import os
import time
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_order():
    """Test placing a real order"""
    print("=" * 60)
    print("🧪 TESTING REAL ORDER PLACEMENT")
    print("=" * 60)
    
    # Find MT4 data path
    mt4_path = None
    base_path = os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal")
    
    if os.path.exists(base_path):
        for folder in os.listdir(base_path):
            terminal_path = os.path.join(base_path, folder, "MQL4", "Files")
            if os.path.exists(terminal_path):
                mt4_path = terminal_path
                break
    
    if not mt4_path:
        print("❌ MT4 data path not found")
        return False
    
    print(f"📁 MT4 path: {mt4_path}")
    
    # File paths
    commands_file = os.path.join(mt4_path, "python_commands.txt")
    responses_file = os.path.join(mt4_path, "python_responses.txt")
    status_file = os.path.join(mt4_path, "mt4_status.txt")
    
    # Check MT4 status
    if not os.path.exists(status_file):
        print("❌ MT4 status file not found")
        return False
    
    with open(status_file, 'r') as f:
        status = f.read()
    
    if "MT4_READY" not in status:
        print("❌ MT4 not ready")
        return False
    
    print("✅ MT4 is ready")
    
    # Test 1: Ping command
    print("\n🔄 Testing ping command...")
    ping_cmd = {"command": "ping"}
    
    with open(commands_file, 'w') as f:
        json.dump(ping_cmd, f)
    
    # Wait for response
    for i in range(10):
        if os.path.exists(responses_file):
            time.sleep(0.1)
            try:
                with open(responses_file, 'r') as f:
                    response = json.load(f)
                os.remove(responses_file)
                
                if response.get('success'):
                    print("✅ Ping successful!")
                    break
                else:
                    print(f"❌ Ping failed: {response}")
                    return False
            except:
                pass
        time.sleep(0.5)
    else:
        print("❌ Ping timeout")
        return False
    
    # Test 2: Get account info
    print("\n📊 Testing account info...")
    account_cmd = {"command": "account_info"}
    
    with open(commands_file, 'w') as f:
        json.dump(account_cmd, f)
    
    # Wait for response
    for i in range(10):
        if os.path.exists(responses_file):
            time.sleep(0.1)
            try:
                with open(responses_file, 'r') as f:
                    response = json.load(f)
                os.remove(responses_file)
                
                if response.get('success'):
                    account_data = response['data']
                    print("✅ Account info received:")
                    print(f"   Login: {account_data.get('login', 'N/A')}")
                    print(f"   Balance: ${account_data.get('balance', 'N/A')}")
                    print(f"   Server: {account_data.get('server', 'N/A')}")
                    break
                else:
                    print(f"❌ Account info failed: {response}")
                    return False
            except Exception as e:
                print(f"❌ Error parsing account response: {e}")
        time.sleep(0.5)
    else:
        print("❌ Account info timeout")
        return False
    
    # Test 3: Get price
    print("\n💰 Testing price data...")
    price_cmd = {"command": "get_price", "symbol": "XAUUSD"}
    
    with open(commands_file, 'w') as f:
        json.dump(price_cmd, f)
    
    # Wait for response
    for i in range(10):
        if os.path.exists(responses_file):
            time.sleep(0.1)
            try:
                with open(responses_file, 'r') as f:
                    response = json.load(f)
                os.remove(responses_file)
                
                if response.get('success'):
                    price_data = response['data']
                    print("✅ Price data received:")
                    print(f"   XAUUSD Bid: {price_data.get('bid', 'N/A')}")
                    print(f"   XAUUSD Ask: {price_data.get('ask', 'N/A')}")
                    break
                else:
                    print(f"❌ Price data failed: {response}")
                    return False
            except Exception as e:
                print(f"❌ Error parsing price response: {e}")
        time.sleep(0.5)
    else:
        print("❌ Price data timeout")
        return False
    
    # Test 4: Place a small test order
    print("\n🎯 Testing order placement...")
    print("⚠️  This will place a REAL order on your demo account!")
    
    order_cmd = {
        "command": "place_order",
        "symbol": "XAUUSD",
        "order_type": "BUY",
        "volume": 0.01,  # Very small volume
        "comment": "Test order from Python"
    }
    
    with open(commands_file, 'w') as f:
        json.dump(order_cmd, f)
    
    # Wait for response
    for i in range(15):  # Longer timeout for order placement
        if os.path.exists(responses_file):
            time.sleep(0.1)
            try:
                with open(responses_file, 'r') as f:
                    response = json.load(f)
                os.remove(responses_file)
                
                if response.get('success'):
                    order_data = response['data']
                    print("🎉 REAL ORDER PLACED SUCCESSFULLY!")
                    print(f"   Ticket: {order_data.get('ticket', 'N/A')}")
                    print(f"   Price: {order_data.get('price', 'N/A')}")
                    print(f"   Volume: {order_data.get('volume', 'N/A')}")
                    print("\n✅ Check your MT4 Trade tab to see the order!")
                    return True
                else:
                    print(f"❌ Order placement failed: {response}")
                    return False
            except Exception as e:
                print(f"❌ Error parsing order response: {e}")
        time.sleep(1)
        print(f"   Waiting for order response... ({i+1}/15)")
    
    print("❌ Order placement timeout")
    return False

def main():
    """Main test function"""
    success = test_real_order()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 REAL TRADING TEST SUCCESSFUL!")
        print("Your bot can now place real trades in MetaTrader 4!")
        print("Check the Trade tab in MT4 to see the test order.")
    else:
        print("❌ REAL TRADING TEST FAILED")
        print("The bot will continue to run in simulation mode.")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
