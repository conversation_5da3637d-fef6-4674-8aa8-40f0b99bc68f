#!/usr/bin/env python3
"""
Simple order placement test
"""

import os
import time
import json

def place_test_order():
    """Place a simple test order"""
    print("🎯 PLACING TEST ORDER...")
    
    # Find MT4 data path
    base_path = os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal")
    mt4_path = None
    
    if os.path.exists(base_path):
        for folder in os.listdir(base_path):
            terminal_path = os.path.join(base_path, folder, "MQL4", "Files")
            if os.path.exists(terminal_path):
                mt4_path = terminal_path
                break
    
    if not mt4_path:
        print("❌ MT4 path not found")
        return False
    
    commands_file = os.path.join(mt4_path, "python_commands.txt")
    responses_file = os.path.join(mt4_path, "python_responses.txt")
    
    # Clean up any existing files
    if os.path.exists(responses_file):
        os.remove(responses_file)
    
    # Place order command
    order_cmd = {
        "command": "place_order",
        "symbol": "XAUUSD",
        "order_type": "BUY", 
        "volume": 0.01,
        "comment": "Python test order"
    }
    
    print(f"📤 Sending command: {order_cmd}")
    
    # Write command
    with open(commands_file, 'w') as f:
        json.dump(order_cmd, f)
    
    print("⏳ Waiting for MT4 response...")
    
    # Wait for response
    for i in range(20):  # 20 second timeout
        if os.path.exists(responses_file):
            time.sleep(0.2)  # Give it time to finish writing
            try:
                with open(responses_file, 'r') as f:
                    response_text = f.read().strip()
                
                print(f"📥 Raw response: {response_text}")
                
                # Try to parse as JSON
                try:
                    response = json.loads(response_text)
                except:
                    print(f"⚠️ Non-JSON response: {response_text}")
                    os.remove(responses_file)
                    return False
                
                os.remove(responses_file)
                
                if response.get('success'):
                    print("🎉 ORDER PLACED SUCCESSFULLY!")
                    print(f"   Ticket: {response.get('data', {}).get('ticket', 'N/A')}")
                    print(f"   Price: {response.get('data', {}).get('price', 'N/A')}")
                    print("\n✅ Check MT4 Trade tab to see the order!")
                    return True
                else:
                    error = response.get('error', 'Unknown error')
                    print(f"❌ Order failed: {error}")
                    return False
                    
            except Exception as e:
                print(f"❌ Error reading response: {e}")
                if os.path.exists(responses_file):
                    os.remove(responses_file)
        
        time.sleep(1)
        print(f"   Waiting... ({i+1}/20)")
    
    print("❌ Timeout - no response from MT4")
    return False

def main():
    print("=" * 50)
    print("🧪 SIMPLE ORDER PLACEMENT TEST")
    print("=" * 50)
    
    success = place_test_order()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS! Real trading is working!")
    else:
        print("❌ Failed - check MT4 settings")
        print("\nTroubleshooting:")
        print("1. Auto Trading button should be GREEN")
        print("2. Expert Advisor should show 😊 smiley face")
        print("3. Check Experts tab for error messages")
    print("=" * 50)

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
