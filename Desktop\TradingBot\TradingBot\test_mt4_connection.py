#!/usr/bin/env python3
"""
Test MT4 connection via socket
"""

import sys
import os
import time

# Add src directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_mt4_socket_connection():
    """Test MT4 socket connection"""
    print("=" * 60)
    print("MetaTrader 4 Connection Test")
    print("=" * 60)
    
    try:
        from brokers.mt4_file import MT4FileBroker
        
        # Configuration
        config = {
            'server': 'FBS-Demo',
            'login': 11653999,
            'password': 'kTYg553z',
            'host': 'localhost',
            'port': 9090,
            'timeout': 10,
            'deviation': 20,
            'magic_number': 234000
        }
        
        print("1. Testing MT4 file broker initialization...")
        broker = MT4FileBroker(config)
        
        print("2. Testing connection status...")
        if broker.is_connected():
            print("✓ Connected to MT4 via file communication!")
            
            print("\n3. Testing account info...")
            account_info = broker.get_account_info()
            print(f"   Account: {account_info['login']}")
            print(f"   Balance: ${account_info['balance']:.2f}")
            print(f"   Server: {account_info['server']}")
            
            print("\n4. Testing price data...")
            symbols = ['XAUUSD', 'EURUSD', 'GBPUSD']
            for symbol in symbols:
                try:
                    price = broker.get_current_price(symbol)
                    print(f"   {symbol}: Bid={price['bid']:.5f}, Ask={price['ask']:.5f}")
                except Exception as e:
                    print(f"   {symbol}: Error getting price - {e}")
            
            print("\n5. Testing symbol info...")
            symbol_info = broker.get_symbol_info('EURUSD')
            print(f"   EURUSD digits: {symbol_info['digits']}")
            print(f"   Min volume: {symbol_info['volume_min']}")
            
            print("\n✓ All tests passed! MT4 connection is working.")
            
        else:
            print("⚠ Not connected to MT4 - running in simulation mode")
            print("This is normal if:")
            print("- MetaTrader 4 is not running")
            print("- PythonBridge Expert Advisor is not attached")
            print("- Socket connection is not established")
            
            print("\n3. Testing simulation mode...")
            account_info = broker.get_account_info()
            print(f"   Simulation Account: {account_info['login']}")
            print(f"   Simulation Balance: ${account_info['balance']:.2f}")
            
            print("\n4. Testing simulation prices...")
            price = broker.get_current_price('XAUUSD')
            print(f"   XAUUSD (simulated): Bid={price['bid']:.2f}, Ask={price['ask']:.2f}")
            
            print("\n✓ Simulation mode working correctly!")
        
        # Cleanup
        broker.disconnect()
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Starting MT4 connection test...\n")
    
    # Test basic imports first
    try:
        import socket
        import json
        print("✓ Basic imports successful")
    except Exception as e:
        print(f"✗ Import error: {e}")
        return False
    
    # Test MT4 connection
    success = test_mt4_socket_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 MT4 CONNECTION TEST COMPLETED SUCCESSFULLY!")
        print("\nNext steps:")
        print("1. If connected to MT4: You're ready to run the trading bot!")
        print("2. If in simulation mode: Follow the MT4_SETUP_GUIDE.md")
        print("\nTo run the trading bot:")
        print("   python run_bot.py")
    else:
        print("❌ MT4 CONNECTION TEST FAILED")
        print("\nTroubleshooting:")
        print("1. Check if Python environment is set up correctly")
        print("2. Follow the MT4_SETUP_GUIDE.md for MT4 setup")
        print("3. Make sure all dependencies are installed")
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
