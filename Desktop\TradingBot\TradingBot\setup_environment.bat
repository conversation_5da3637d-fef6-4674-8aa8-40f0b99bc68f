@echo off
echo ================================================
echo Setting up Trading Bot Environment
echo ================================================

REM Add Python to PATH for this session
set "PATH=%LOCALAPPDATA%\Programs\Python\Python313;%LOCALAPPDATA%\Programs\Python\Python313\Scripts;%PATH%"

echo Step 1: Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    pause
    exit /b 1
)

echo.
echo Step 2: Creating virtual environment...
cd TradingBot
python -m venv env
if %errorlevel% neq 0 (
    echo ERROR: Failed to create virtual environment!
    pause
    exit /b 1
)

echo.
echo Step 3: Activating virtual environment...
call env\Scripts\activate.bat

echo.
echo Step 4: Upgrading pip...
python -m pip install --upgrade pip

echo.
echo Step 5: Installing dependencies...
pip install MetaTrader4 pandas numpy ta-lib python-dotenv requests schedule textblob nltk pytest

echo.
echo ================================================
echo Environment setup complete!
echo ================================================
echo.
echo To activate the environment in the future, run:
echo   cd TradingBot
echo   env\Scripts\activate.bat
echo.
echo To test the connection, run:
echo   python test_connection.py
echo.
echo To start the trading bot, run:
echo   python src\main.py
echo.
pause
