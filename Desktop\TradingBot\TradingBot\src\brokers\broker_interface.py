# src/brokers/broker_interface.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

class IBroker(ABC):
    """
    Abstract base class defining the interface for all broker implementations.
    Concrete broker classes (MT4, MT5, Alpaca, etc.) should implement this interface.
    """
    
    @abstractmethod
    def __init__(self, config: Dict[str, Any]):
        """Initialize the broker with configuration"""
        pass
    
    @abstractmethod
    def connect(self) -> bool:
        """Connect to the broker's API/trading platform"""
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """Disconnect from the broker's API/trading platform"""
        pass
    
    @abstractmethod
    def get_account_info(self) -> Dict[str, Any]:
        """
        Get account information (balance, equity, margin, etc.)
        Returns:
            Dictionary with account information
        """
        pass
    
    @abstractmethod
    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get current position for a specific symbol
        Args:
            symbol: Trading symbol (e.g., 'XAUUSD', 'NAS100')
        Returns:
            Dictionary with position details or None if no position exists
        """
        pass
    
    @abstractmethod
    def get_all_positions(self) -> List[Dict[str, Any]]:
        """
        Get all open positions
        Returns:
            List of dictionaries with position details
        """
        pass
    
    @abstractmethod
    def get_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """
        Get pending orders
        Args:
            symbol: Optional filter for specific symbol
        Returns:
            List of dictionaries with order details
        """
        pass
    
    @abstractmethod
    def place_order(
        self,
        symbol: str,
        order_type: str,
        volume: float,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        comment: str = "",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Place a new order
        Args:
            symbol: Trading symbol
            order_type: Order type (e.g., 'BUY', 'SELL', 'BUY_LIMIT', etc.)
            volume: Trade volume/lots
            stop_loss: Optional stop loss price
            take_profit: Optional take profit price
            comment: Optional order comment
            **kwargs: Additional broker-specific parameters
        Returns:
            Dictionary with order execution details
        """
        pass
    
    @abstractmethod
    def modify_order(
        self,
        ticket: int,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        **kwargs
    ) -> bool:
        """
        Modify an existing order
        Args:
            ticket: Order ticket number
            stop_loss: New stop loss price
            take_profit: New take profit price
            **kwargs: Additional broker-specific parameters
        Returns:
            True if modification was successful
        """
        pass
    
    @abstractmethod
    def close_position(self, symbol: str, portion: float = 1.0) -> bool:
        """
        Close an open position
        Args:
            symbol: Trading symbol of the position to close
            portion: Portion of the position to close (0.0 to 1.0)
        Returns:
            True if position was closed successfully
        """
        pass
    
    @abstractmethod
    def cancel_order(self, ticket: int) -> bool:
        """
        Cancel a pending order
        Args:
            ticket: Order ticket number
        Returns:
            True if order was canceled successfully
        """
        pass
    
    @abstractmethod
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get information about a trading symbol
        Args:
            symbol: Trading symbol
        Returns:
            Dictionary with symbol information (digits, lot size, etc.)
        """
        pass
    
    @abstractmethod
    def get_current_price(self, symbol: str) -> Dict[str, float]:
        """
        Get current bid/ask prices for a symbol
        Args:
            symbol: Trading symbol
        Returns:
            Dictionary with 'bid' and 'ask' prices
        """
        pass
    
    @abstractmethod
    def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        start: Optional[datetime] = None,
        end: Optional[datetime] = None,
        count: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get historical price data
        Args:
            symbol: Trading symbol
            timeframe: Timeframe (e.g., 'M1', 'H1', 'D1')
            start: Optional start datetime
            end: Optional end datetime
            count: Optional number of bars to retrieve
        Returns:
            List of dictionaries with OHLC data
        """
        pass
    
    @abstractmethod
    def get_market_depth(self, symbol: str) -> Dict[str, List[Dict[str, float]]]:
        """
        Get market depth (order book) data
        Args:
            symbol: Trading symbol
        Returns:
            Dictionary with 'bids' and 'asks' lists
        """
        pass
    
    @abstractmethod
    def get_server_time(self) -> datetime:
        """
        Get broker server time
        Returns:
            Current server datetime
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        Check if connection to broker is active
        Returns:
            True if connected
        """
        pass
    
    @abstractmethod
    def get_margin_requirements(self, symbol: str, volume: float) -> Dict[str, float]:
        """
        Calculate margin requirements for a potential trade
        Args:
            symbol: Trading symbol
            volume: Trade volume
        Returns:
            Dictionary with margin information
        """
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """
        Get list of available trading symbols
        Returns:
            List of symbol names
        """
        pass
    
    @abstractmethod
    def get_trade_history(
        self,
        start: datetime,
        end: datetime,
        symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get historical trade data
        Args:
            start: Start datetime
            end: End datetime
            symbol: Optional symbol filter
        Returns:
            List of historical trades
        """
        pass