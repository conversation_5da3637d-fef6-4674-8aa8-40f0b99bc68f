"""
MetaTrader4 compatibility layer using MetaTrader5
This module provides MT4-like interface using MT5 functionality
"""

import MetaTrader5 as mt5
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

# Initialize logger
logger = logging.getLogger(__name__)

# MT4-style constants (mapped to MT5)
ORDER_BUY = mt5.ORDER_TYPE_BUY
ORDER_SELL = mt5.ORDER_TYPE_SELL
ORDER_BUY_LIMIT = mt5.ORDER_TYPE_BUY_LIMIT
ORDER_SELL_LIMIT = mt5.ORDER_TYPE_SELL_LIMIT
ORDER_BUY_STOP = mt5.ORDER_TYPE_BUY_STOP
ORDER_SELL_STOP = mt5.ORDER_TYPE_SELL_STOP

POSITION_TYPE_BUY = mt5.POSITION_TYPE_BUY
POSITION_TYPE_SELL = mt5.POSITION_TYPE_SELL

TRADE_ACTION_DEAL = mt5.TRADE_ACTION_DEAL
TRADE_ACTION_SLTP = mt5.TRADE_ACTION_SLTP
TRADE_ACTION_REMOVE = mt5.TRADE_ACTION_REMOVE

ORDER_TIME_GTC = mt5.ORDER_TIME_GTC
ORDER_FILLING_FOK = mt5.ORDER_FILLING_FOK

TRADE_RETCODE_DONE = mt5.TRADE_RETCODE_DONE

DEAL_TYPE_BUY = mt5.DEAL_TYPE_BUY
DEAL_TYPE_SELL = mt5.DEAL_TYPE_SELL

# Timeframe constants
TIMEFRAME_M1 = mt5.TIMEFRAME_M1
TIMEFRAME_M5 = mt5.TIMEFRAME_M5
TIMEFRAME_M15 = mt5.TIMEFRAME_M15
TIMEFRAME_M30 = mt5.TIMEFRAME_M30
TIMEFRAME_H1 = mt5.TIMEFRAME_H1
TIMEFRAME_H4 = mt5.TIMEFRAME_H4
TIMEFRAME_D1 = mt5.TIMEFRAME_D1
TIMEFRAME_W1 = mt5.TIMEFRAME_W1
TIMEFRAME_MN1 = mt5.TIMEFRAME_MN1


def initialize():
    """Initialize MT5 connection"""
    try:
        if not mt5.initialize():
            logger.error("Failed to initialize MT5")
            return False
        logger.info("MT5 initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing MT5: {e}")
        return False


def login(login: int, password: str, server: str, timeout: int = 60000, portable: bool = False):
    """Login to MT5 with MT4-style parameters"""
    try:
        # For MT5, we need to authorize after initialization
        authorized = mt5.login(login, password=password, server=server, timeout=timeout)
        if not authorized:
            logger.error(f"Failed to login to {server} with account {login}")
            return False
        
        logger.info(f"Successfully logged in to {server} with account {login}")
        return True
    except Exception as e:
        logger.error(f"Login error: {e}")
        return False


def shutdown():
    """Shutdown MT5 connection"""
    try:
        mt5.shutdown()
        logger.info("MT5 shutdown complete")
    except Exception as e:
        logger.error(f"Shutdown error: {e}")


def last_error():
    """Get last error code"""
    return mt5.last_error()


def account_info():
    """Get account information"""
    return mt5.account_info()


def terminal_info():
    """Get terminal information"""
    return mt5.terminal_info()


def symbol_info(symbol: str):
    """Get symbol information"""
    return mt5.symbol_info(symbol)


def symbol_info_tick(symbol: str):
    """Get current tick for symbol"""
    return mt5.symbol_info_tick(symbol)


def symbols_get():
    """Get available symbols"""
    return mt5.symbols_get()


def positions_get(symbol: str = None, ticket: int = None):
    """Get positions"""
    if ticket:
        return mt5.positions_get(ticket=ticket)
    elif symbol:
        return mt5.positions_get(symbol=symbol)
    else:
        return mt5.positions_get()


def orders_get(symbol: str = None, ticket: int = None):
    """Get orders"""
    if ticket:
        return mt5.orders_get(ticket=ticket)
    elif symbol:
        return mt5.orders_get(symbol=symbol)
    else:
        return mt5.orders_get()


def order_send(request: Dict[str, Any]):
    """Send trading request"""
    try:
        result = mt5.order_send(request)
        return result
    except Exception as e:
        logger.error(f"Order send error: {e}")
        # Return a mock failed result
        class MockResult:
            def __init__(self):
                self.retcode = 10013  # Invalid request
                self.comment = str(e)
                self.order = 0
                self.deal = 0
                self.volume = 0
                self.price = 0.0
        
        return MockResult()


def copy_rates_from_pos(symbol: str, timeframe: int, start_pos: int, count: int):
    """Copy rates from position"""
    return mt5.copy_rates_from_pos(symbol, timeframe, start_pos, count)


def copy_rates_range(symbol: str, timeframe: int, date_from: datetime, date_to: datetime):
    """Copy rates from date range"""
    return mt5.copy_rates_range(symbol, timeframe, date_from, date_to)


def history_deals_get(date_from: datetime, date_to: datetime, group: str = None):
    """Get historical deals"""
    if group:
        return mt5.history_deals_get(date_from, date_to, group=group)
    else:
        return mt5.history_deals_get(date_from, date_to)


def history_orders_get(date_from: datetime, date_to: datetime, group: str = None):
    """Get historical orders"""
    if group:
        return mt5.history_orders_get(date_from, date_to, group=group)
    else:
        return mt5.history_orders_get(date_from, date_to)


# Additional utility functions for compatibility
def is_connected():
    """Check if connected to terminal"""
    try:
        terminal = mt5.terminal_info()
        return terminal is not None
    except:
        return False


def get_version():
    """Get MT5 version info"""
    try:
        return mt5.version()
    except:
        return None
