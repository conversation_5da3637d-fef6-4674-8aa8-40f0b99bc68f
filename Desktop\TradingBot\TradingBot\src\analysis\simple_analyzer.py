# src/analysis/simple_analyzer.py
"""
Simple market analyzer that provides basic trading signals
This is a simplified version for initial testing
"""

import pandas as pd
import numpy as np
from typing import Dict, Any
import logging
from ..utils.logger import get_logger


class SimpleMarketAnalyzer:
    """Simplified market analyzer for basic trading signals"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(__name__)
    
    def analyze_market(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform simple market analysis
        Returns analysis with recommendation
        """
        try:
            if len(data) < 20:
                return {
                    'symbol': symbol,
                    'recommendation': 'HOLD',
                    'confidence': 0.0,
                    'error': 'Insufficient data'
                }
            
            # Simple moving average crossover strategy
            close_prices = data['close'].values
            
            # Calculate simple moving averages
            sma_fast = self._calculate_sma(close_prices, 10)
            sma_slow = self._calculate_sma(close_prices, 20)
            
            # Current values
            current_fast = sma_fast[-1]
            current_slow = sma_slow[-1]
            prev_fast = sma_fast[-2]
            prev_slow = sma_slow[-2]
            
            # Generate signal
            recommendation = 'HOLD'
            confidence = 0.5
            
            # Golden cross (fast MA crosses above slow MA)
            if prev_fast <= prev_slow and current_fast > current_slow:
                recommendation = 'BUY'
                confidence = 0.7
            # Death cross (fast MA crosses below slow MA)
            elif prev_fast >= prev_slow and current_fast < current_slow:
                recommendation = 'SELL'
                confidence = 0.7
            # Strong uptrend
            elif current_fast > current_slow * 1.01:  # 1% above
                recommendation = 'BUY'
                confidence = 0.6
            # Strong downtrend
            elif current_fast < current_slow * 0.99:  # 1% below
                recommendation = 'SELL'
                confidence = 0.6
            
            return {
                'symbol': symbol,
                'timestamp': pd.Timestamp.now(),
                'recommendation': recommendation,
                'confidence': confidence,
                'signals': {
                    'sma_fast': current_fast,
                    'sma_slow': current_slow,
                    'trend': 'UP' if current_fast > current_slow else 'DOWN'
                },
                'strategy': 'SMA_Crossover'
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return {
                'symbol': symbol,
                'recommendation': 'HOLD',
                'confidence': 0.0,
                'error': str(e)
            }
    
    def _calculate_sma(self, prices: np.ndarray, period: int) -> np.ndarray:
        """Calculate Simple Moving Average"""
        sma = np.full(len(prices), np.nan)
        for i in range(period - 1, len(prices)):
            sma[i] = np.mean(prices[i - period + 1:i + 1])
        return sma
