# Trading Bot Setup Guide

## Requirements

1. Python 3.8+
2. MetaTrader 4 installed
3. Windows OS (for MT4 integration)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/trading-bot.git
   cd trading-bot
   ```

2. Install dependencies:
   ```bash
   python scripts/install_dependencies.py
   ```

3. Setup MetaTrader 4 integration:
   ```bash
   python scripts/setup_mt4.py
   ```

4. Configure your settings in `config/config.json`

## Running the Bot

Start the trading bot:
```bash
python src/main.py
```

## Configuration

Edit `config/config.json` to customize:

- Broker settings
- Risk parameters
- Trading strategies
- Market instruments
