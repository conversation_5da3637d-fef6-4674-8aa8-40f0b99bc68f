# src/analysis/technical.py
import pandas as pd
import numpy as np
import talib
from typing import Dict, Any

class TechnicalAnalyzer:
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('technical', {})
        self.rsi_period = self.config.get('rsi_period', 14)
        self.macd_fast = self.config.get('macd_fast', 12)
        self.macd_slow = self.config.get('macd_slow', 26)
        self.macd_signal = self.config.get('macd_signal', 9)
        self.bb_period = self.config.get('bb_period', 20)
        self.ema_periods = self.config.get('ema_periods', [50, 200])

    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze technical indicators for given market data
        Returns dictionary with signals and scores
        """
        if len(data) < max(self.macd_slow, self.rsi_period, self.bb_period):
            return {'error': 'Insufficient data for analysis'}

        close_prices = data['close'].values
        high_prices = data['high'].values
        low_prices = data['low'].values
        
        # Calculate indicators
        indicators = {
            'rsi': self._calculate_rsi(close_prices),
            'macd': self._calculate_macd(close_prices),
            'bollinger': self._calculate_bollinger_bands(close_prices),
            'ema': self._calculate_emas(close_prices),
            'adx': self._calculate_adx(high_prices, low_prices, close_prices),
            'stochastic': self._calculate_stochastic(high_prices, low_prices, close_prices)
        }
        
        # Generate signals
        signals = self._generate_signals(indicators, close_prices)
        
        # Calculate composite score (0-1)
        score = self._calculate_composite_score(indicators, signals)
        
        return {
            'indicators': indicators,
            'signals': signals,
            'score': score,
            'timestamp': data.index[-1]
        }

    def _calculate_rsi(self, close_prices: np.ndarray) -> Dict[str, float]:
        rsi = talib.RSI(close_prices, timeperiod=self.rsi_period)
        return {
            'value': float(rsi[-1]),
            'overbought': 70,
            'oversold': 30
        }

    def _calculate_macd(self, close_prices: np.ndarray) -> Dict[str, float]:
        macd, signal, _ = talib.MACD(
            close_prices,
            fastperiod=self.macd_fast,
            slowperiod=self.macd_slow,
            signalperiod=self.macd_signal
        )
        return {
            'macd': float(macd[-1]),
            'signal': float(signal[-1]),
            'histogram': float(macd[-1] - signal[-1])
        }

    def _calculate_bollinger_bands(self, close_prices: np.ndarray) -> Dict[str, float]:
        upper, middle, lower = talib.BBANDS(
            close_prices,
            timeperiod=self.bb_period,
            nbdevup=2,
            nbdevdn=2,
            matype=0
        )
        return {
            'upper': float(upper[-1]),
            'middle': float(middle[-1]),
            'lower': float(lower[-1]),
            'width': float((upper[-1] - lower[-1]) / middle[-1])
        }

    def _calculate_emas(self, close_prices: np.ndarray) -> Dict[str, float]:
        emas = {}
        for period in self.ema_periods:
            ema = talib.EMA(close_prices, timeperiod=period)
            emas[f'ema_{period}'] = float(ema[-1])
        return emas

    def _calculate_adx(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, float]:
        adx = talib.ADX(high, low, close, timeperiod=14)
        return {
            'adx': float(adx[-1]),
            'trend_strength': 'Strong' if adx[-1] > 25 else 'Weak'
        }

    def _calculate_stochastic(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, float]:
        slowk, slowd = talib.STOCH(high, low, close,
                                  fastk_period=14,
                                  slowk_period=3,
                                  slowk_matype=0,
                                  slowd_period=3,
                                  slowd_matype=0)
        return {
            'k': float(slowk[-1]),
            'd': float(slowd[-1]),
            'overbought': 80,
            'oversold': 20
        }

    def _generate_signals(self, indicators: Dict[str, Any], close_prices: np.ndarray) -> Dict[str, str]:
        signals = {}
        current_price = close_prices[-1]
        
        # RSI signals
        rsi = indicators['rsi']['value']
        signals['rsi'] = 'oversold' if rsi < 30 else 'overbought' if rsi > 70 else 'neutral'
        
        # MACD signals
        macd_hist = indicators['macd']['histogram']
        prev_macd_hist = macd_hist - (indicators['macd']['macd'] - indicators['macd']['signal'])
        signals['macd'] = 'bullish' if macd_hist > 0 and prev_macd_hist <= 0 else \
                         'bearish' if macd_hist < 0 and prev_macd_hist >= 0 else 'neutral'
        
        # Bollinger Bands signals
        bb = indicators['bollinger']
        signals['bollinger'] = 'oversold' if current_price <= bb['lower'] else \
                              'overbought' if current_price >= bb['upper'] else 'neutral'
        
        # EMA signals
        ema_50 = indicators['ema']['ema_50']
        ema_200 = indicators['ema']['ema_200']
        signals['ema_cross'] = 'golden' if ema_50 > ema_200 and indicators['ema'].get('prev_ema_50', 0) <= indicators['ema'].get('prev_ema_200', 0) else \
                              'death' if ema_50 < ema_200 and indicators['ema'].get('prev_ema_50', 0) >= indicators['ema'].get('prev_ema_200', 0) else 'neutral'
        
        # ADX signals
        signals['trend_strength'] = indicators['adx']['trend_strength']
        
        return signals

    def _calculate_composite_score(self, indicators: Dict[str, Any], signals: Dict[str, str]) -> float:
        """Calculate a composite technical score between 0 and 1"""
        score = 0.5  # Neutral baseline
        
        # RSI contribution
        rsi = indicators['rsi']['value']
        if rsi < 30:
            score += 0.15
        elif rsi > 70:
            score -= 0.15
        
        # MACD contribution
        if signals['macd'] == 'bullish':
            score += 0.1
        elif signals['macd'] == 'bearish':
            score -= 0.1
        
        # Bollinger Bands contribution
        if signals['bollinger'] == 'oversold':
            score += 0.1
        elif signals['bollinger'] == 'overbought':
            score -= 0.1
        
        # EMA cross contribution
        if signals['ema_cross'] == 'golden':
            score += 0.15
        elif signals['ema_cross'] == 'death':
            score -= 0.15
        
        # ADX trend strength contribution
        if signals['trend_strength'] == 'Strong':
            # Strengthen existing bias
            score = score + 0.1 if score > 0.5 else score - 0.1
        
        # Normalize score between 0 and 1
        return max(0, min(1, score))