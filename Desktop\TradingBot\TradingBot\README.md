# Automated Trading Bot

A Python-based trading bot for MetaTrader 4 that trades stocks, gold, NASDAQ, and Dow Jones.

## Features

- Multiple trading strategies (mean reversion, trend following, news-based)
- Comprehensive risk management
- Real-time and historical data integration
- News sentiment analysis
- 24-hour trade capability

## Quick Start

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure your MT4 connection in `config/mt4_config.json`

3. Run the bot:
   ```bash
   python src/main.py
   ```

## Documentation

See the [docs](docs/) folder for detailed setup and API documentation.