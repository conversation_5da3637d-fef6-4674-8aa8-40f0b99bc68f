# src/utils/logger.py
import logging
import os
from datetime import datetime
from typing import Optional

def setup_logging(log_dir: str = "logs", level: int = logging.INFO):
    """Configure logging system"""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"trading_bot_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
def get_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """Get a configured logger instance"""
    logger = logging.getLogger(name)
    if level is not None:
        logger.setLevel(level)
    return logger
