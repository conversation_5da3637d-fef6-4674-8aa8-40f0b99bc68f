#!/usr/bin/env python3
"""
Test script to verify MT4 connection and basic functionality
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.logger import setup_logging, get_logger
from src.brokers.mt4 import MT4<PERSON>roker

def test_mt4_connection():
    """Test MT4 connection with your credentials"""
    setup_logging()
    logger = get_logger(__name__)
    
    # Your MT4 configuration
    config = {
        'server': 'FBS-Demo',
        'login': 11653999,
        'password': 'kTYg553z',
        'timeout': 60000,
        'portable': False,
        'deviation': 20,
        'magic_number': 234000
    }
    
    try:
        logger.info("Testing MT4 connection...")
        logger.info(f"Server: {config['server']}")
        logger.info(f"Login: {config['login']}")
        
        # Initialize broker
        broker = MT4Broker(config)
        
        # Test connection
        if broker.is_connected():
            logger.info("✓ Successfully connected to MT4!")
            
            # Get account info
            account_info = broker.get_account_info()
            logger.info(f"Account Balance: ${account_info['balance']:.2f}")
            logger.info(f"Account Equity: ${account_info['equity']:.2f}")
            logger.info(f"Account Currency: {account_info['currency']}")
            
            # Test getting price for a symbol
            try:
                price = broker.get_current_price('XAUUSD')
                logger.info(f"XAUUSD Price - Bid: {price['bid']}, Ask: {price['ask']}")
            except Exception as e:
                logger.warning(f"Could not get XAUUSD price: {e}")
            
            # Test getting available symbols
            try:
                symbols = broker.get_available_symbols()
                logger.info(f"Available symbols: {len(symbols)} found")
                if symbols:
                    logger.info(f"First 10 symbols: {symbols[:10]}")
            except Exception as e:
                logger.warning(f"Could not get symbols: {e}")
            
            logger.info("✓ All basic tests passed!")
            
        else:
            logger.error("✗ Failed to connect to MT4")
            return False
            
    except Exception as e:
        logger.error(f"✗ Connection test failed: {e}")
        return False
    finally:
        try:
            broker.disconnect()
            logger.info("Disconnected from MT4")
        except:
            pass
    
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("MT4 Connection Test")
    print("=" * 50)
    
    success = test_mt4_connection()
    
    if success:
        print("\n✓ Connection test completed successfully!")
        print("You can now run the main trading bot with: python src/main.py")
    else:
        print("\n✗ Connection test failed!")
        print("Please check your MT4 installation and credentials.")
