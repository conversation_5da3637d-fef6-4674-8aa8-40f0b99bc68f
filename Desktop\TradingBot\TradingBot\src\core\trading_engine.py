# src/core/trading_engine.py
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from ..brokers.mt4_file import MT4FileBroker, TradingError
from ..analysis.simple_analyzer import SimpleMarketAnalyzer
from ..core.risk_manager import RiskManager
from ..utils.logger import get_logger

class TradingEngine:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(__name__)

        # Initialize components
        try:
            self.broker = MT4FileBroker(config['mt4'])
            self.analyzer = SimpleMarketAnalyzer(config['analysis'])
            self.risk_manager = RiskManager(config['risk'], self.broker)
            self.open_positions = {}

            self.logger.info("Trading engine initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize trading engine: {e}")
            raise
        
    def run(self):
        """Main trading loop"""
        self.logger.info("Starting trading engine main loop")

        while True:
            try:
                # Check if broker is still connected
                if not self.broker.is_connected():
                    self.logger.error("Lost connection to broker. Attempting to reconnect...")
                    if not self.broker.connect():
                        self.logger.error("Failed to reconnect. Stopping trading.")
                        break

                self.check_market_conditions()
                self.monitor_positions()
                time.sleep(self.config['scan_interval'])

            except KeyboardInterrupt:
                self.logger.info("Trading stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Error in main loop: {str(e)}", exc_info=True)
                time.sleep(10)  # Wait before retrying
                
    def check_market_conditions(self):
        """Check market conditions for all instruments"""
        try:
            for symbol in self.config['instruments']:
                try:
                    # Get historical data for analysis
                    historical_data = self.broker.get_historical_data(
                        symbol=symbol,
                        timeframe='H1',  # 1-hour timeframe
                        count=100  # Last 100 bars
                    )

                    if not historical_data or len(historical_data) < 50:
                        self.logger.warning(f"Insufficient data for {symbol}")
                        continue

                    # Convert to pandas DataFrame
                    import pandas as pd
                    df = pd.DataFrame(historical_data)
                    df.set_index('time', inplace=True)

                    # Perform analysis
                    analysis = self.analyzer.analyze_market(symbol, df)

                    if analysis and analysis.get('recommendation') not in ['HOLD', None]:
                        self.evaluate_trade(symbol, analysis)

                except Exception as e:
                    self.logger.error(f"Error analyzing {symbol}: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"Error in check_market_conditions: {e}")
    
    def evaluate_trade(self, symbol: str, analysis: Dict[str, Any]):
        """Evaluate whether to place a trade based on analysis"""
        try:
            # Check if we already have a position
            if symbol in self.open_positions:
                return

            # Check risk limits
            if not self.risk_manager.can_open_position():
                self.logger.info(f"Risk limits prevent opening position for {symbol}")
                return

            # Calculate position size based on risk
            position_size = self.risk_manager.calculate_position_size(
                symbol=symbol,
                risk_pct=self.config['risk'].get('max_risk_per_trade', 0.02)
            )

            if position_size <= 0:
                self.logger.info(f"Position size too small for {symbol}")
                return

            # Get current price
            price_data = self.broker.get_current_price(symbol)
            entry_price = price_data['ask'] if analysis['recommendation'] == 'BUY' else price_data['bid']

            # Calculate stop loss and take profit
            stop_loss = self.calculate_stop_loss(symbol, analysis, entry_price)
            take_profit = self.calculate_take_profit(symbol, analysis, entry_price)

            # Place order
            order_type = analysis['recommendation']  # 'BUY' or 'SELL'

            result = self.broker.place_order(
                symbol=symbol,
                order_type=order_type,
                volume=position_size,
                stop_loss=stop_loss,
                take_profit=take_profit,
                comment=f"Auto trade: {analysis.get('strategy', 'unknown')}"
            )

            # Track position
            self.open_positions[symbol] = {
                'ticket': result.get('ticket'),
                'entry_time': datetime.now(),
                'entry_price': entry_price,
                'order_type': order_type,
                'volume': position_size,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'analysis': analysis
            }

            self.logger.info(f"Opened {order_type} position for {symbol} at {entry_price}")

        except TradingError as e:
            self.logger.error(f"Trading error for {symbol}: {e}")
        except Exception as e:
            self.logger.error(f"Error evaluating trade for {symbol}: {e}")
    
    def monitor_positions(self):
        """Monitor open positions and manage them"""
        try:
            for symbol, position in list(self.open_positions.items()):
                try:
                    # Check if position still exists in broker
                    broker_position = self.broker.get_position(position.get('ticket'))
                    if broker_position is None:
                        # Position was closed externally
                        self.logger.info(f"Position for {symbol} was closed externally")
                        del self.open_positions[symbol]
                        continue

                    # Check if position should be closed based on time (24hr rule)
                    if datetime.now() - position['entry_time'] >= timedelta(hours=24):
                        self.close_position(symbol, reason="24hr time limit")
                        continue

                    # Check for manual stop loss/take profit (in case MT4 didn't trigger)
                    price_data = self.broker.get_current_price(symbol)
                    current_price = price_data['bid'] if position['order_type'] == 'BUY' else price_data['ask']

                    if (position['order_type'] == 'BUY' and current_price <= position['stop_loss']) or \
                       (position['order_type'] == 'SELL' and current_price >= position['stop_loss']):
                        self.close_position(symbol, reason="Stop loss triggered")
                    elif (position['order_type'] == 'BUY' and current_price >= position['take_profit']) or \
                         (position['order_type'] == 'SELL' and current_price <= position['take_profit']):
                        self.close_position(symbol, reason="Take profit triggered")

                except Exception as e:
                    self.logger.error(f"Error monitoring position for {symbol}: {e}")

        except Exception as e:
            self.logger.error(f"Error in monitor_positions: {e}")

    def close_position(self, symbol: str, reason: str = ""):
        """Close a position"""
        try:
            if symbol not in self.open_positions:
                self.logger.warning(f"No tracked position found for {symbol}")
                return False

            position = self.open_positions[symbol]
            ticket = position.get('ticket')

            if ticket and self.broker.close_position(ticket):
                self.logger.info(f"Closed position for {symbol}. Reason: {reason}")
                del self.open_positions[symbol]
                return True
            else:
                self.logger.error(f"Failed to close position for {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"Error closing position for {symbol}: {e}")
            return False

    def calculate_stop_loss(self, symbol: str, analysis: Dict[str, Any], entry_price: float) -> Optional[float]:
        """Calculate stop loss price"""
        try:
            # Simple ATR-based stop loss (2% for now)
            stop_loss_pct = 0.02  # 2%

            if analysis['recommendation'] == 'BUY':
                return entry_price * (1 - stop_loss_pct)
            else:  # SELL
                return entry_price * (1 + stop_loss_pct)

        except Exception as e:
            self.logger.error(f"Error calculating stop loss for {symbol}: {e}")
            return None

    def calculate_take_profit(self, symbol: str, analysis: Dict[str, Any], entry_price: float) -> Optional[float]:
        """Calculate take profit price"""
        try:
            # Simple risk-reward ratio (1:2)
            take_profit_pct = 0.04  # 4% (2x stop loss)

            if analysis['recommendation'] == 'BUY':
                return entry_price * (1 + take_profit_pct)
            else:  # SELL
                return entry_price * (1 - take_profit_pct)

        except Exception as e:
            self.logger.error(f"Error calculating take profit for {symbol}: {e}")
            return None

    def shutdown(self):
        """Shutdown the trading engine"""
        try:
            self.logger.info("Shutting down trading engine...")

            # Close all open positions
            for symbol in list(self.open_positions.keys()):
                self.close_position(symbol, "Engine shutdown")

            # Disconnect from broker
            self.broker.disconnect()

            self.logger.info("Trading engine shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")