#!/usr/bin/env python3
"""
Simple test to check basic functionality
"""

import sys
import os

print("=" * 50)
print("Simple Trading Bot Test")
print("=" * 50)

# Test 1: Python and imports
print("1. Testing Python and basic imports...")
try:
    import pandas as pd
    import numpy as np
    print("✓ pandas and numpy imported successfully")
except Exception as e:
    print(f"✗ Error importing basic packages: {e}")
    sys.exit(1)

# Test 2: MetaTrader5 import
print("\n2. Testing MetaTrader5 import...")
try:
    import MetaTrader5 as mt5
    print("✓ MetaTrader5 imported successfully")
    print(f"  Version: {mt5.version()}")
except Exception as e:
    print(f"✗ Error importing MetaTrader5: {e}")

# Test 3: Our custom modules
print("\n3. Testing custom modules...")
try:
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    from src.utils.logger import setup_logging, get_logger
    print("✓ Logger module imported successfully")
except Exception as e:
    print(f"✗ Error importing logger: {e}")

# Test 4: Configuration loading
print("\n4. Testing configuration...")
try:
    import json
    config_file = os.path.join(os.path.dirname(__file__), 'TradingBot', 'config', 'config.json')
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config = json.load(f)
        print("✓ Configuration loaded successfully")
        print(f"  MT4 Server: {config['mt4']['server']}")
        print(f"  MT4 Login: {config['mt4']['login']}")
    else:
        print(f"✗ Configuration file not found: {config_file}")
except Exception as e:
    print(f"✗ Error loading configuration: {e}")

# Test 5: MetaTrader connection (basic check)
print("\n5. Testing MetaTrader connection...")
try:
    import MetaTrader5 as mt5
    
    # Initialize MT5
    if mt5.initialize():
        print("✓ MT5 initialized successfully")
        
        # Try to get terminal info
        terminal_info = mt5.terminal_info()
        if terminal_info:
            print(f"  Terminal: {terminal_info.name}")
            print(f"  Build: {terminal_info.build}")
        
        mt5.shutdown()
        print("✓ MT5 shutdown successfully")
    else:
        print("✗ Failed to initialize MT5")
        print("  Note: This is expected if MetaTrader 5 is not installed or running")
        
except Exception as e:
    print(f"✗ Error testing MT5 connection: {e}")

print("\n" + "=" * 50)
print("Test Summary:")
print("- If all tests pass, the environment is ready")
print("- If MT5 connection fails, install MetaTrader 5 from:")
print("  https://www.metatrader5.com/en/download")
print("- Make sure MetaTrader 5 is running before testing connection")
print("=" * 50)
