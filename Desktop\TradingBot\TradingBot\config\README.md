# Configuration Guide

## config.json

Main configuration file for the trading bot.

### MT4 Configuration

```json
{
    "mt4": {
        "server": "FBS-Demo",        // MT4 server name
        "login": ********,           // Your account number
        "password": "kTYg553z",      // Your account password
        "timeout": 60000,            // Connection timeout in ms
        "portable": false,           // Use portable mode
        "deviation": 20,             // Maximum price deviation in points
        "magic_number": 234000       // Unique identifier for bot trades
    }
}
```

### Risk Management

```json
{
    "risk": {
        "max_risk_per_trade": 0.02,  // Maximum 2% risk per trade
        "max_daily_loss": 0.05,      // Stop trading if 5% daily loss
        "max_open_positions": 5,     // Maximum concurrent positions
        "position_sizing": "fixed",   // "fixed" or "percentage"
        "fixed_lot_size": 0.1        // Fixed lot size when using fixed sizing
    }
}
```

### Trading Instruments

Supported instruments:
- **XAUUSD**: Gold vs USD
- **NAS100**: NASDAQ 100 Index
- **US30**: Dow Jones Industrial Average
- **EURUSD**: Euro vs USD
- **GBPUSD**: British Pound vs USD

### Analysis Settings

```json
{
    "analysis": {
        "timeframes": ["M15", "H1", "H4"],  // Timeframes to analyze
        "indicators": ["RSI", "MACD", "EMA", "SMA"],  // Technical indicators
        "sentiment_weight": 0.3,            // Weight for sentiment analysis
        "technical_weight": 0.7             // Weight for technical analysis
    }
}
```

## Security Notes

- Keep your MT4 credentials secure
- Use demo accounts for testing
- Never share your configuration files
- Consider using environment variables for sensitive data
