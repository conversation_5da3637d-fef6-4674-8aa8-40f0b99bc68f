"""
Simple TA-Lib replacement with basic technical indicators
This provides the essential functions needed by the trading bot
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional


def SMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """Simple Moving Average"""
    return pd.Series(close).rolling(window=timeperiod).mean().values


def EMA(close: np.ndarray, timeperiod: int = 30) -> np.ndarray:
    """Exponential Moving Average"""
    return pd.Series(close).ewm(span=timeperiod).mean().values


def RSI(close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """Relative Strength Index"""
    close_series = pd.Series(close)
    delta = close_series.diff()
    
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=timeperiod).mean()
    avg_loss = loss.rolling(window=timeperiod).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi.values


def MACD(close: np.ndarray, fastperiod: int = 12, slowperiod: int = 26, signalperiod: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """MACD - Moving Average Convergence Divergence"""
    close_series = pd.Series(close)
    
    ema_fast = close_series.ewm(span=fastperiod).mean()
    ema_slow = close_series.ewm(span=slowperiod).mean()
    
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signalperiod).mean()
    histogram = macd_line - signal_line
    
    return macd_line.values, signal_line.values, histogram.values


def BBANDS(close: np.ndarray, timeperiod: int = 20, nbdevup: float = 2, nbdevdn: float = 2, matype: int = 0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Bollinger Bands"""
    close_series = pd.Series(close)
    
    # Calculate middle band (SMA)
    middle = close_series.rolling(window=timeperiod).mean()
    
    # Calculate standard deviation
    std = close_series.rolling(window=timeperiod).std()
    
    # Calculate upper and lower bands
    upper = middle + (std * nbdevup)
    lower = middle - (std * nbdevdn)
    
    return upper.values, middle.values, lower.values


def STOCH(high: np.ndarray, low: np.ndarray, close: np.ndarray, 
          fastk_period: int = 5, slowk_period: int = 3, slowd_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
    """Stochastic Oscillator"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    # Calculate %K
    lowest_low = low_series.rolling(window=fastk_period).min()
    highest_high = high_series.rolling(window=fastk_period).max()
    
    k_percent = 100 * ((close_series - lowest_low) / (highest_high - lowest_low))
    
    # Smooth %K to get slow %K
    slow_k = k_percent.rolling(window=slowk_period).mean()
    
    # Calculate %D (signal line)
    slow_d = slow_k.rolling(window=slowd_period).mean()
    
    return slow_k.values, slow_d.values


def ADX(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """Average Directional Index"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    # Calculate True Range
    tr1 = high_series - low_series
    tr2 = abs(high_series - close_series.shift(1))
    tr3 = abs(low_series - close_series.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # Calculate Directional Movement
    dm_plus = high_series.diff()
    dm_minus = -low_series.diff()
    
    dm_plus[dm_plus < 0] = 0
    dm_minus[dm_minus < 0] = 0
    
    # Smooth the values
    tr_smooth = tr.rolling(window=timeperiod).mean()
    dm_plus_smooth = dm_plus.rolling(window=timeperiod).mean()
    dm_minus_smooth = dm_minus.rolling(window=timeperiod).mean()
    
    # Calculate DI+ and DI-
    di_plus = 100 * (dm_plus_smooth / tr_smooth)
    di_minus = 100 * (dm_minus_smooth / tr_smooth)
    
    # Calculate DX
    dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
    
    # Calculate ADX
    adx = dx.rolling(window=timeperiod).mean()
    
    return adx.values


def ATR(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """Average True Range"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    # Calculate True Range
    tr1 = high_series - low_series
    tr2 = abs(high_series - close_series.shift(1))
    tr3 = abs(low_series - close_series.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # Calculate ATR
    atr = tr.rolling(window=timeperiod).mean()
    
    return atr.values


def CCI(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """Commodity Channel Index"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    # Calculate Typical Price
    tp = (high_series + low_series + close_series) / 3
    
    # Calculate SMA of Typical Price
    sma_tp = tp.rolling(window=timeperiod).mean()
    
    # Calculate Mean Deviation
    mad = tp.rolling(window=timeperiod).apply(lambda x: np.mean(np.abs(x - x.mean())))
    
    # Calculate CCI
    cci = (tp - sma_tp) / (0.015 * mad)
    
    return cci.values


def WILLR(high: np.ndarray, low: np.ndarray, close: np.ndarray, timeperiod: int = 14) -> np.ndarray:
    """Williams %R"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    # Calculate highest high and lowest low over the period
    highest_high = high_series.rolling(window=timeperiod).max()
    lowest_low = low_series.rolling(window=timeperiod).min()
    
    # Calculate Williams %R
    willr = -100 * ((highest_high - close_series) / (highest_high - lowest_low))
    
    return willr.values


# Additional utility functions
def TRANGE(high: np.ndarray, low: np.ndarray, close: np.ndarray) -> np.ndarray:
    """True Range"""
    high_series = pd.Series(high)
    low_series = pd.Series(low)
    close_series = pd.Series(close)
    
    tr1 = high_series - low_series
    tr2 = abs(high_series - close_series.shift(1))
    tr3 = abs(low_series - close_series.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    return tr.values


def STDDEV(close: np.ndarray, timeperiod: int = 5, nbdev: float = 1) -> np.ndarray:
    """Standard Deviation"""
    return pd.Series(close).rolling(window=timeperiod).std().values * nbdev
