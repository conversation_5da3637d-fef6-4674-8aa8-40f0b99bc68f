# src/strategies/mean_reversion.py
import pandas as pd
import talib
from typing import Dict, Any
from .base_strategy import BaseStrategy

class MeanReversionStrategy(BaseStrategy):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.rsi_period = config.get('rsi_period', 14)
        self.bb_period = config.get('bb_period', 20)
        self.overbought = config.get('overbought', 70)
        self.oversold = config.get('oversold', 30)
        
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform mean reversion analysis"""
        close = data['close'].values
        
        # Calculate indicators
        rsi = talib.RSI(close, timeperiod=self.rsi_period)
        upper, middle, lower = talib.BBANDS(
            close,
            timeperiod=self.bb_period,
            nbdevup=2,
            nbdevdn=2,
            matype=0
        )
        
        # Current values
        current_rsi = rsi[-1]
        current_close = close[-1]
        current_upper = upper[-1]
        current_lower = lower[-1]
        
        # Generate signals
        signals = {
            'rsi': current_rsi,
            'bb_upper': current_upper,
            'bb_lower': current_lower,
            'price_at_band': (current_close - current_lower) / (current_upper - current_lower)
        }
        
        return {
            'indicators': {
                'rsi': rsi,
                'bollinger_upper': upper,
                'bollinger_lower': lower
            },
            'signals': signals,
            'current_price': current_close
        }
        
    def get_signal(self, analysis: Dict[str, Any]) -> str:
        """Generate trading signal"""
        signals = analysis['signals']
        current_rsi = signals['rsi']
        price_at_band = signals['price_at_band']
        
        # Buy signal: Oversold conditions
        if current_rsi < self.oversold or price_at_band < 0.1:
            return 'BUY'
            
        # Sell signal: Overbought conditions
        if current_rsi > self.overbought or price_at_band > 0.9:
            return 'SELL'
            
        return 'HOLD'
