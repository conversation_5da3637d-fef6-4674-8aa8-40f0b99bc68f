# src/brokers/mt4_socket.py
"""
MetaTrader 4 Socket-based connection
This connects to MT4 via socket communication with an Expert Advisor
"""

import socket
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from .broker_interface import IBroker
from ..utils.logger import get_logger


class TradingError(Exception):
    """Custom exception for trading errors"""
    pass


class MT4SocketBroker(IBroker):
    """MetaTrader 4 broker implementation using socket communication"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = get_logger(__name__)
        self.connected = False
        self.socket = None
        
        # Socket configuration
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 9090)
        self.timeout = config.get('timeout', 10)
        
        # Try to connect
        if not self.connect():
            self.logger.warning("Could not connect to MT4 via socket. Running in simulation mode.")
    
    def connect(self) -> bool:
        """Connect to MetaTrader 4 via socket"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            
            # Send login command
            login_cmd = {
                'command': 'login',
                'server': self.config['server'],
                'login': self.config['login'],
                'password': self.config['password']
            }
            
            response = self._send_command(login_cmd)
            if response and response.get('success'):
                self.connected = True
                self.logger.info(f"Successfully connected to MT4 via socket")
                return True
            else:
                self.logger.error(f"MT4 login failed: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error connecting to MT4: {e}")
            self.connected = False
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from MetaTrader 4"""
        try:
            if self.socket:
                self.socket.close()
            self.connected = False
            self.logger.info("Disconnected from MT4")
            return True
        except Exception as e:
            self.logger.error(f"Error disconnecting from MT4: {e}")
            return False
    
    def is_connected(self) -> bool:
        """Check if connection to MT4 is active"""
        if not self.connected or not self.socket:
            return False
        
        try:
            # Send a ping command to check connection
            ping_cmd = {'command': 'ping'}
            response = self._send_command(ping_cmd)
            return response is not None
        except:
            return False
    
    def _send_command(self, command: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send command to MT4 and get response"""
        try:
            if not self.socket:
                return None
            
            # Send command
            message = json.dumps(command) + '\n'
            self.socket.send(message.encode('utf-8'))
            
            # Receive response
            response = self.socket.recv(4096).decode('utf-8')
            return json.loads(response.strip())
            
        except Exception as e:
            self.logger.error(f"Error sending command to MT4: {e}")
            return None
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        try:
            if not self.is_connected():
                # Return mock data for simulation
                return {
                    'login': self.config['login'],
                    'balance': 10000.0,
                    'equity': 10000.0,
                    'margin': 0.0,
                    'margin_free': 10000.0,
                    'margin_level': 0.0,
                    'currency': 'USD',
                    'server': self.config['server'],
                    'leverage': 100
                }
            
            cmd = {'command': 'account_info'}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                return response['data']
            else:
                raise TradingError(f"Failed to get account info: {response}")
                
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            # Return mock data as fallback
            return {
                'login': self.config['login'],
                'balance': 10000.0,
                'equity': 10000.0,
                'margin': 0.0,
                'margin_free': 10000.0,
                'margin_level': 0.0,
                'currency': 'USD',
                'server': self.config['server'],
                'leverage': 100
            }
    
    def get_current_price(self, symbol: str) -> Dict[str, float]:
        """Get current bid/ask prices for a symbol"""
        try:
            if not self.is_connected():
                # Return mock prices for simulation
                mock_prices = {
                    'XAUUSD': {'bid': 2000.50, 'ask': 2000.70},
                    'EURUSD': {'bid': 1.0850, 'ask': 1.0852},
                    'GBPUSD': {'bid': 1.2650, 'ask': 1.2652},
                    'NAS100': {'bid': 15000.0, 'ask': 15001.0},
                    'US30': {'bid': 35000.0, 'ask': 35001.0}
                }
                
                price = mock_prices.get(symbol, {'bid': 1.0000, 'ask': 1.0001})
                price['time'] = time.time()
                return price
            
            cmd = {'command': 'get_price', 'symbol': symbol}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                return response['data']
            else:
                raise TradingError(f"Failed to get price for {symbol}: {response}")
                
        except Exception as e:
            self.logger.error(f"Error getting price for {symbol}: {e}")
            # Return mock price as fallback
            return {'bid': 1.0000, 'ask': 1.0001, 'time': time.time()}
    
    def place_order(self, symbol: str, order_type: str, volume: float, 
                   stop_loss: Optional[float] = None, take_profit: Optional[float] = None, 
                   comment: str = "", **kwargs) -> Dict[str, Any]:
        """Place a new order"""
        try:
            cmd = {
                'command': 'place_order',
                'symbol': symbol,
                'order_type': order_type,
                'volume': volume,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'comment': comment,
                'magic': self.config.get('magic_number', 234000),
                'deviation': self.config.get('deviation', 20)
            }
            
            if not self.is_connected():
                # Simulate order placement
                self.logger.info(f"SIMULATION: {order_type} order for {symbol}, volume: {volume}")
                return {
                    'ticket': int(time.time()),  # Mock ticket
                    'retcode': 0,
                    'deal': int(time.time()),
                    'order': int(time.time()),
                    'volume': volume,
                    'price': self.get_current_price(symbol)['ask' if order_type == 'BUY' else 'bid'],
                    'comment': comment
                }
            
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                self.logger.info(f"Order placed successfully: {symbol} {order_type} {volume} lots")
                return response['data']
            else:
                raise TradingError(f"Order failed: {response}")
                
        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            raise TradingError(f"Failed to place order: {e}")
    
    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current position for a specific symbol"""
        try:
            if not self.is_connected():
                return None  # No positions in simulation mode
            
            cmd = {'command': 'get_position', 'symbol': symbol}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                return response['data']
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting position for {symbol}: {e}")
            return None
    
    def get_all_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions"""
        try:
            if not self.is_connected():
                return []  # No positions in simulation mode
            
            cmd = {'command': 'get_all_positions'}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                return response['data']
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting all positions: {e}")
            return []
    
    def close_position(self, ticket: int) -> bool:
        """Close a position"""
        try:
            if not self.is_connected():
                self.logger.info(f"SIMULATION: Closing position {ticket}")
                return True
            
            cmd = {'command': 'close_position', 'ticket': ticket}
            response = self._send_command(cmd)
            
            if response and response.get('success'):
                self.logger.info(f"Position {ticket} closed successfully")
                return True
            else:
                self.logger.error(f"Failed to close position {ticket}: {response}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error closing position {ticket}: {e}")
            return False
    
    # Implement remaining required methods with simulation fallbacks
    def get_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        return []
    
    def modify_position(self, ticket: int, stop_loss: Optional[float] = None, 
                       take_profit: Optional[float] = None) -> bool:
        return True
    
    def cancel_order(self, ticket: int) -> bool:
        return True
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        return {
            'name': symbol,
            'digits': 5 if 'JPY' not in symbol else 3,
            'point': 0.00001 if 'JPY' not in symbol else 0.001,
            'spread': 2,
            'volume_min': 0.01,
            'volume_max': 100.0,
            'volume_step': 0.01,
            'contract_size': 100000,
            'margin_initial': 0.01,
            'currency_base': symbol[:3],
            'currency_profit': symbol[3:],
            'currency_margin': 'USD'
        }
    
    def get_historical_data(self, symbol: str, timeframe: str, 
                           start: Optional[datetime] = None, end: Optional[datetime] = None, 
                           count: Optional[int] = None) -> List[Dict[str, Any]]:
        # Generate mock historical data for simulation
        import random
        data = []
        base_price = 1.0000
        
        if symbol == 'XAUUSD':
            base_price = 2000.0
        elif symbol in ['NAS100', 'US30']:
            base_price = 15000.0
        
        for i in range(count or 100):
            price = base_price + random.uniform(-50, 50)
            data.append({
                'time': datetime.now(),
                'open': price,
                'high': price + random.uniform(0, 10),
                'low': price - random.uniform(0, 10),
                'close': price + random.uniform(-5, 5),
                'volume': random.randint(100, 1000)
            })
        
        return data
    
    def get_market_depth(self, symbol: str) -> Dict[str, List[Dict[str, float]]]:
        return {'bids': [], 'asks': []}
    
    def get_server_time(self) -> datetime:
        return datetime.now()
    
    def get_margin_requirements(self, symbol: str, volume: float) -> Dict[str, float]:
        return {'margin_required': volume * 1000, 'margin_currency': 'USD'}
    
    def get_available_symbols(self) -> List[str]:
        return ['XAUUSD', 'EURUSD', 'GBPUSD', 'NAS100', 'US30']
    
    def get_trade_history(self, start: datetime, end: datetime, 
                         symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        return []
