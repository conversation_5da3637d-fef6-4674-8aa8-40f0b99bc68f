//+------------------------------------------------------------------+
//|                                                 PythonBridge.mq4 |
//|                        Copyright 2024, Trading Bot Python Bridge |
//|                                File-based communication with Python|
//+------------------------------------------------------------------+
#property copyright "Trading Bot Python Bridge"
#property link      ""
#property version   "2.00"
#property strict

// File-based communication variables
string commands_file = "python_commands.txt";
string responses_file = "python_responses.txt";
string status_file = "mt4_status.txt";
bool bridge_active = false;
datetime last_command_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("Python Bridge EA starting (File-based communication)...");

    // Initialize file-based communication
    if(InitializeFileBridge())
    {
        Print("File bridge initialized successfully");
        bridge_active = true;
        return(INIT_SUCCEEDED);
    }
    else
    {
        Print("Failed to initialize file bridge");
        return(INIT_FAILED);
    }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    CleanupFileBridge();
    Print("Python Bridge EA stopped");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check for file-based commands every tick
    if(bridge_active)
    {
        HandleFileCommands();
        UpdateStatus();
    }
}

//+------------------------------------------------------------------+
//| Initialize file-based bridge                                    |
//+------------------------------------------------------------------+
bool InitializeFileBridge()
{
    // Create status file to indicate MT4 is ready
    int handle = FileOpen(status_file, FILE_WRITE|FILE_TXT);
    if(handle != INVALID_HANDLE)
    {
        FileWrite(handle, "MT4_READY");
        FileWrite(handle, "Account: " + IntegerToString(AccountNumber()));
        FileWrite(handle, "Server: " + AccountServer());
        FileWrite(handle, "Time: " + TimeToString(TimeCurrent()));
        FileClose(handle);
        return true;
    }
    else
    {
        Print("Failed to create status file");
        return false;
    }
}

//+------------------------------------------------------------------+
//| Handle file-based commands                                      |
//+------------------------------------------------------------------+
void HandleFileCommands()
{
    // Check if there's a new command file
    if(FileIsExist(commands_file))
    {
        datetime file_time = (datetime)FileGetInteger(commands_file, FILE_MODIFY_DATE);

        // Only process if file is newer than last processed command
        if(file_time > last_command_time)
        {
            string command = ReadCommandFile();
            if(StringLen(command) > 0)
            {
                string response = ProcessCommand(command);
                WriteResponseFile(response);
                last_command_time = file_time;

                // Delete the command file after processing
                FileDelete(commands_file);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update status file                                              |
//+------------------------------------------------------------------+
void UpdateStatus()
{
    static datetime last_update = 0;

    // Update status every 5 seconds
    if(TimeCurrent() - last_update >= 5)
    {
        int handle = FileOpen(status_file, FILE_WRITE|FILE_TXT);
        if(handle != INVALID_HANDLE)
        {
            FileWrite(handle, "MT4_READY");
            FileWrite(handle, "Account: " + IntegerToString(AccountNumber()));
            FileWrite(handle, "Balance: " + DoubleToString(AccountBalance(), 2));
            FileWrite(handle, "Equity: " + DoubleToString(AccountEquity(), 2));
            FileWrite(handle, "Server: " + AccountServer());
            FileWrite(handle, "Time: " + TimeToString(TimeCurrent()));
            FileWrite(handle, "LastUpdate: " + IntegerToString(TimeCurrent()));
            FileClose(handle);
        }
        last_update = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Read command from file                                          |
//+------------------------------------------------------------------+
string ReadCommandFile()
{
    int handle = FileOpen(commands_file, FILE_READ|FILE_TXT);
    if(handle != INVALID_HANDLE)
    {
        string command = "";
        while(!FileIsEnding(handle))
        {
            command += FileReadString(handle);
        }
        FileClose(handle);
        return command;
    }
    return "";
}

//+------------------------------------------------------------------+
//| Write response to file                                          |
//+------------------------------------------------------------------+
void WriteResponseFile(string response)
{
    int handle = FileOpen(responses_file, FILE_WRITE|FILE_TXT);
    if(handle != INVALID_HANDLE)
    {
        FileWrite(handle, response);
        FileClose(handle);
    }
}

//+------------------------------------------------------------------+
//| Process command from Python                                     |
//+------------------------------------------------------------------+
string ProcessCommand(string command)
{
    Print("Received command: ", command);
    
    // Parse JSON command (simplified)
    if(StringFind(command, "\"command\":\"ping\"") >= 0 || StringFind(command, "\"command\": \"ping\"") >= 0)
    {
        return "{\"success\":true,\"data\":\"pong\"}";
    }
    else if(StringFind(command, "\"command\":\"account_info\"") >= 0 || StringFind(command, "\"command\": \"account_info\"") >= 0)
    {
        return GetAccountInfoJSON();
    }
    else if(StringFind(command, "\"command\":\"get_price\"") >= 0 || StringFind(command, "\"command\": \"get_price\"") >= 0)
    {
        string symbol = ExtractSymbolFromCommand(command);
        return GetPriceJSON(symbol);
    }
    else if(StringFind(command, "\"command\":\"place_order\"") >= 0 || StringFind(command, "\"command\": \"place_order\"") >= 0)
    {
        return PlaceOrderFromCommand(command);
    }
    else if(StringFind(command, "\"command\":\"get_position\"") >= 0 || StringFind(command, "\"command\": \"get_position\"") >= 0)
    {
        string symbol = ExtractSymbolFromCommand(command);
        return GetPositionJSON(symbol);
    }
    else if(StringFind(command, "\"command\":\"close_position\"") >= 0 || StringFind(command, "\"command\": \"close_position\"") >= 0)
    {
        int ticket = ExtractTicketFromCommand(command);
        return ClosePositionJSON(ticket);
    }
    
    return "{\"success\":false,\"error\":\"Unknown command\"}";
}



//+------------------------------------------------------------------+
//| Get account info as JSON                                        |
//+------------------------------------------------------------------+
string GetAccountInfoJSON()
{
    string json = "{\"success\":true,\"data\":{";
    json += "\"login\":" + IntegerToString(AccountNumber()) + ",";
    json += "\"balance\":" + DoubleToString(AccountBalance(), 2) + ",";
    json += "\"equity\":" + DoubleToString(AccountEquity(), 2) + ",";
    json += "\"margin\":" + DoubleToString(AccountMargin(), 2) + ",";
    json += "\"margin_free\":" + DoubleToString(AccountFreeMargin(), 2) + ",";
    json += "\"currency\":\"" + AccountCurrency() + "\",";
    json += "\"server\":\"" + AccountServer() + "\",";
    json += "\"leverage\":" + IntegerToString(AccountLeverage());
    json += "}}";
    return json;
}

//+------------------------------------------------------------------+
//| Get price as JSON                                               |
//+------------------------------------------------------------------+
string GetPriceJSON(string symbol)
{
    double bid = MarketInfo(symbol, MODE_BID);
    double ask = MarketInfo(symbol, MODE_ASK);
    
    if(bid > 0 && ask > 0)
    {
        string json = "{\"success\":true,\"data\":{";
        json += "\"bid\":" + DoubleToString(bid, Digits) + ",";
        json += "\"ask\":" + DoubleToString(ask, Digits) + ",";
        json += "\"time\":" + IntegerToString(TimeCurrent());
        json += "}}";
        return json;
    }
    else
    {
        return "{\"success\":false,\"error\":\"Invalid symbol or no price data\"}";
    }
}

//+------------------------------------------------------------------+
//| Extract symbol from command                                     |
//+------------------------------------------------------------------+
string ExtractSymbolFromCommand(string command)
{
    int start = StringFind(command, "\"symbol\":\"") + 10;
    if(start == 9) start = StringFind(command, "\"symbol\": \"") + 11; // Handle spaces
    int end = StringFind(command, "\"", start);
    if(start > 9 && end > start)
    {
        return StringSubstr(command, start, end - start);
    }
    return "";
}

//+------------------------------------------------------------------+
//| Extract order type from command                                 |
//+------------------------------------------------------------------+
string ExtractOrderTypeFromCommand(string command)
{
    int start = StringFind(command, "\"order_type\":\"") + 14;
    if(start == 13) start = StringFind(command, "\"order_type\": \"") + 15; // Handle spaces
    int end = StringFind(command, "\"", start);
    if(start > 13 && end > start)
    {
        return StringSubstr(command, start, end - start);
    }
    return "";
}

//+------------------------------------------------------------------+
//| Extract volume from command                                     |
//+------------------------------------------------------------------+
double ExtractVolumeFromCommand(string command)
{
    int start = StringFind(command, "\"volume\":") + 9;
    if(start == 8) start = StringFind(command, "\"volume\": ") + 10; // Handle spaces
    int end = StringFind(command, ",", start);
    if(end == -1) end = StringFind(command, "}", start);

    if(start > 8 && end > start)
    {
        string volumeStr = StringSubstr(command, start, end - start);
        return StringToDouble(volumeStr);
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Extract stop loss from command                                  |
//+------------------------------------------------------------------+
double ExtractStopLossFromCommand(string command)
{
    int start = StringFind(command, "\"stop_loss\":") + 12;
    if(start == 11) start = StringFind(command, "\"stop_loss\": ") + 13; // Handle spaces

    // Check for null value
    if(StringFind(command, "\"stop_loss\":null") >= 0 || StringFind(command, "\"stop_loss\": null") >= 0)
        return 0.0;

    int end = StringFind(command, ",", start);
    if(end == -1) end = StringFind(command, "}", start);

    if(start > 11 && end > start)
    {
        string slStr = StringSubstr(command, start, end - start);
        return StringToDouble(slStr);
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Extract take profit from command                                |
//+------------------------------------------------------------------+
double ExtractTakeProfitFromCommand(string command)
{
    int start = StringFind(command, "\"take_profit\":") + 14;
    if(start == 13) start = StringFind(command, "\"take_profit\": ") + 15; // Handle spaces

    // Check for null value
    if(StringFind(command, "\"take_profit\":null") >= 0 || StringFind(command, "\"take_profit\": null") >= 0)
        return 0.0;

    int end = StringFind(command, ",", start);
    if(end == -1) end = StringFind(command, "}", start);

    if(start > 13 && end > start)
    {
        string tpStr = StringSubstr(command, start, end - start);
        return StringToDouble(tpStr);
    }
    return 0.0;
}

//+------------------------------------------------------------------+
//| Extract ticket from command                                     |
//+------------------------------------------------------------------+
int ExtractTicketFromCommand(string command)
{
    int start = StringFind(command, "\"ticket\":") + 9;
    int end = StringFind(command, ",", start);
    if(end == -1) end = StringFind(command, "}", start);

    if(start > 8 && end > start)
    {
        string ticketStr = StringSubstr(command, start, end - start);
        return (int)StringToInteger(ticketStr);  // Explicit cast to avoid warning
    }
    return -1;
}

//+------------------------------------------------------------------+
//| Place order from command                                        |
//+------------------------------------------------------------------+
string PlaceOrderFromCommand(string command)
{
    // Extract order parameters
    string symbol = ExtractSymbolFromCommand(command);
    string orderType = ExtractOrderTypeFromCommand(command);
    double volume = ExtractVolumeFromCommand(command);

    if(symbol == "" || orderType == "" || volume <= 0)
    {
        return "{\"success\":false,\"error\":\"Invalid order parameters\"}";
    }

    // Determine order type
    int cmd = -1;
    if(orderType == "BUY") cmd = OP_BUY;
    else if(orderType == "SELL") cmd = OP_SELL;
    else
    {
        return "{\"success\":false,\"error\":\"Invalid order type\"}";
    }

    // Get current price
    double price = 0;
    if(cmd == OP_BUY) price = MarketInfo(symbol, MODE_ASK);
    else price = MarketInfo(symbol, MODE_BID);

    if(price <= 0)
    {
        return "{\"success\":false,\"error\":\"Invalid price for symbol\"}";
    }

    // Extract stop loss and take profit
    double stopLoss = ExtractStopLossFromCommand(command);
    double takeProfit = ExtractTakeProfitFromCommand(command);

    // Place the order with SL/TP
    int ticket = OrderSend(symbol, cmd, volume, price, 3, stopLoss, takeProfit, "Python Bot", 234000, 0, clrNONE);

    if(ticket > 0)
    {
        string json = "{\"success\":true,\"data\":{";
        json += "\"ticket\":" + IntegerToString(ticket) + ",";
        json += "\"retcode\":0,";
        json += "\"price\":" + DoubleToString(price, Digits) + ",";
        json += "\"volume\":" + DoubleToString(volume, 2) + ",";
        json += "\"comment\":\"Order placed successfully\"";
        json += "}}";
        return json;
    }
    else
    {
        int error = GetLastError();
        string json = "{\"success\":false,\"error\":\"Order failed: " + IntegerToString(error) + "\"}";
        return json;
    }
}

//+------------------------------------------------------------------+
//| Get position as JSON                                            |
//+------------------------------------------------------------------+
string GetPositionJSON(string symbol)
{
    // Check for open positions for the symbol
    for(int i = 0; i < OrdersTotal(); i++)
    {
        if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if(OrderSymbol() == symbol)
            {
                string json = "{\"success\":true,\"data\":{";
                json += "\"ticket\":" + IntegerToString(OrderTicket()) + ",";
                json += "\"symbol\":\"" + OrderSymbol() + "\",";
                json += "\"volume\":" + DoubleToString(OrderLots(), 2) + ",";
                json += "\"type\":\"" + (OrderType() == OP_BUY ? "BUY" : "SELL") + "\",";
                json += "\"price_open\":" + DoubleToString(OrderOpenPrice(), Digits) + ",";
                json += "\"profit\":" + DoubleToString(OrderProfit(), 2);
                json += "}}";
                return json;
            }
        }
    }
    
    return "{\"success\":false,\"error\":\"No position found\"}";
}

//+------------------------------------------------------------------+
//| Close position as JSON                                          |
//+------------------------------------------------------------------+
string ClosePositionJSON(int ticket)
{
    if(OrderSelect(ticket, SELECT_BY_TICKET))
    {
        bool result = OrderClose(ticket, OrderLots(), 
                                OrderType() == OP_BUY ? Bid : Ask, 3);
        
        if(result)
        {
            return "{\"success\":true,\"data\":\"Position closed\"}";
        }
        else
        {
            return "{\"success\":false,\"error\":\"Failed to close position\"}";
        }
    }
    
    return "{\"success\":false,\"error\":\"Position not found\"}";
}

//+------------------------------------------------------------------+
//| Cleanup file bridge                                            |
//+------------------------------------------------------------------+
void CleanupFileBridge()
{
    // Delete communication files
    if(FileIsExist(commands_file))
        FileDelete(commands_file);

    if(FileIsExist(responses_file))
        FileDelete(responses_file);

    // Update status file to indicate shutdown
    int handle = FileOpen(status_file, FILE_WRITE|FILE_TXT);
    if(handle != INVALID_HANDLE)
    {
        FileWrite(handle, "MT4_STOPPED");
        FileWrite(handle, "Time: " + TimeToString(TimeCurrent()));
        FileClose(handle);
    }

    bridge_active = false;
}
