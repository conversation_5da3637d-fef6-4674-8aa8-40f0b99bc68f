<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>24c80d00-f506-4f34-8ec1-40e7f0e1d7d6</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>src\main.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>TradingBot</Name>
    <RootNamespace>TradingBot</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="docs\scripts\install_dependencies.py" />
    <Compile Include="docs\scripts\setup_mt4.py" />
    <Compile Include="src\analysis\fundamental.py" />
    <Compile Include="src\analysis\market_analyzer.py" />
    <Compile Include="src\analysis\sentiment.py" />
    <Compile Include="src\analysis\technical.py" />
    <Compile Include="src\analysis\__init__.py" />
    <Compile Include="src\brokers\broker_interface.py" />
    <Compile Include="src\brokers\mt4.py" />
    <Compile Include="src\brokers\__init__.py" />
    <Compile Include="src\core\order_manager.py" />
    <Compile Include="src\core\portfolio_manager.py" />
    <Compile Include="src\core\risk_manager.py" />
    <Compile Include="src\core\trading_engine.py" />
    <Compile Include="src\core\__init__.py" />
    <Compile Include="src\data\historical.py" />
    <Compile Include="src\data\news.py" />
    <Compile Include="src\data\realtime.py" />
    <Compile Include="src\data\__init__.py" />
    <Compile Include="src\main.py" />
    <Compile Include="src\strategies\base_strategy.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\strategies\mean_reversion.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\strategies\news_based.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\strategies\trend_following.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\strategies\__init__.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\utils\helpers.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\utils\logger.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\utils\scheduler.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="src\utils\__init__.py">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="test\__init__.py" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="config\" />
    <Folder Include="docs\" />
    <Folder Include="docs\scripts\" />
    <Folder Include="test\" />
    <Folder Include="src\" />
    <Folder Include="src\core\" />
    <Folder Include="src\analysis\" />
    <Folder Include="src\brokers\" />
    <Folder Include="src\data\" />
    <Folder Include="src\strategies\" />
    <Folder Include="src\utils\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="docs\api.md" />
    <Content Include="docs\setup.md" />
    <Content Include="README.md" />
    <Content Include="requirements.txt" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="env\">
      <Id>env</Id>
      <Version>3.13</Version>
      <Description>env (Python 3.13 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>