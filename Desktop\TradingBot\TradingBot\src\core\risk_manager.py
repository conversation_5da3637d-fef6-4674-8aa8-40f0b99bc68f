# src/core/risk_manager.py
import logging
from datetime import datetime, date
from typing import Dict, Optional
from ..brokers import IBroker
from ..utils.logger import get_logger

class RiskManager:
    def __init__(self, config: Dict, broker: Optional[IBroker] = None):
        """
        Initialize risk manager with configuration

        Args:
            config: Risk management configuration dictionary
            broker: Broker instance for account info
        """
        self.config = config
        self.broker = broker
        self.logger = get_logger(__name__)

        # Risk parameters
        self.max_risk_per_trade = config.get('max_risk_per_trade', 0.02)  # 2% of account
        self.max_daily_loss = config.get('max_daily_loss', 0.05)  # 5% of account
        self.max_open_positions = config.get('max_open_positions', 5)
        self.position_sizing = config.get('position_sizing', 'fixed')
        self.fixed_lot_size = config.get('fixed_lot_size', 0.1)

        self.daily_pnl = 0.0
        self.today = None
        self.open_positions_count = 0

    def set_broker(self, broker: IBroker):
        """Set broker instance for account info access"""
        self.broker = broker

    def can_open_position(self) -> bool:
        """Check if we can open a new position based on risk limits"""
        try:
            # Check maximum open positions
            if self.open_positions_count >= self.max_open_positions:
                self.logger.info(f"Maximum open positions ({self.max_open_positions}) reached")
                return False

            # Check daily loss limit
            if self._exceeds_daily_loss_limit():
                self.logger.info("Daily loss limit exceeded")
                return False

            # Check if broker is available
            if not self.broker:
                self.logger.error("No broker instance available")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error checking if can open position: {e}")
            return False

    def calculate_position_size(
        self,
        symbol: str,
        risk_pct: Optional[float] = None,
        entry_price: Optional[float] = None,
        stop_loss: Optional[float] = None
    ) -> float:
        """
        Calculate appropriate position size based on risk parameters

        Args:
            symbol: Trading symbol
            risk_pct: Risk percentage (overrides config if provided)
            entry_price: Proposed entry price
            stop_loss: Stop loss price

        Returns:
            Position size in lots/units
        """
        try:
            if not self.broker:
                self.logger.error("No broker instance available for position sizing")
                return 0.0

            # Use fixed lot size if configured
            if self.position_sizing == 'fixed':
                return self.fixed_lot_size

            if risk_pct is None:
                risk_pct = self.max_risk_per_trade

            # Get account balance
            account_info = self.broker.get_account_info()
            balance = account_info['balance']

            # Calculate absolute risk amount
            risk_amount = balance * risk_pct

            # Get current price if not provided
            if entry_price is None:
                price_data = self.broker.get_current_price(symbol)
                entry_price = price_data['ask']  # Use ask price as default

            # Calculate position size
            if stop_loss is not None and entry_price is not None:
                risk_per_unit = abs(entry_price - stop_loss)
                if risk_per_unit <= 0:
                    return 0.0

                position_size = risk_amount / risk_per_unit
            else:
                # Fallback to percentage-based if no stop loss
                position_size = risk_amount / entry_price

            # Apply position size limits
            max_size = self._calculate_max_position_size(symbol)
            position_size = min(position_size, max_size)

            # Ensure minimum lot size
            symbol_info = self.broker.get_symbol_info(symbol)
            min_lot = symbol_info.get('volume_min', 0.01)
            position_size = max(position_size, min_lot)

            return round(position_size, 2)

        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {e}")
            return self.fixed_lot_size  # Fallback to fixed size

    def _calculate_max_position_size(self, symbol: str) -> float:
        """Calculate maximum allowed position size for a symbol"""
        try:
            account_info = self.broker.get_account_info()
            balance = account_info['balance']

            # Get symbol info
            symbol_info = self.broker.get_symbol_info(symbol)
            max_volume = symbol_info.get('volume_max', 100.0)

            # Calculate max by account balance (conservative approach)
            max_by_balance = balance * 0.1  # Max 10% of balance per position

            # Convert to lots (assuming standard lot sizes)
            if 'XAU' in symbol:  # Gold
                max_lots = max_by_balance / 100  # $100 per 0.01 lot approximately
            else:  # Forex and indices
                max_lots = max_by_balance / 1000  # Conservative estimate

            return min(max_lots, max_volume, 10.0)  # Cap at 10 lots max

        except Exception as e:
            self.logger.error(f"Error calculating max position size for {symbol}: {e}")
            return 1.0  # Conservative fallback

    def validate_trade(
        self,
        symbol: str,
        order_type: str,
        volume: float,
        stop_loss: float
    ) -> bool:
        """
        Validate if a trade meets risk requirements
        
        Args:
            symbol: Trading symbol
            order_type: Order type (BUY/SELL)
            volume: Trade volume
            stop_loss: Stop loss price
            
        Returns:
            True if trade is acceptable, False if it violates risk rules
        """
        # Check daily loss limit
        if self._exceeds_daily_loss_limit():
            return False
            
        # Check position size
        max_size = self._calculate_max_position_size(symbol)
        if volume > max_size:
            return False
            
        # Check stop loss distance
        current_price = self.broker.get_current_price(symbol)
        price = current_price['ask'] if order_type == 'BUY' else current_price['bid']
        
        sl_distance = abs(price - stop_loss) / price
        if sl_distance > self.config.get('max_sl_distance', 0.1):  # 10% max SL distance
            return False
            
        return True

    def _exceeds_daily_loss_limit(self) -> bool:
        """Check if daily loss limit has been exceeded"""
        try:
            if not self.broker:
                return False

            account_info = self.broker.get_account_info()
            today = datetime.now().date()

            if self.today != today:
                self.today = today
                self.daily_pnl = 0.0

            # Get today's trade history
            try:
                start_of_day = datetime.combine(today, datetime.min.time())
                closed_today = self.broker.get_trade_history(start_of_day, datetime.now())

                today_pnl = sum(t.get('profit', 0) for t in closed_today)
                self.daily_pnl = today_pnl

            except Exception as e:
                self.logger.warning(f"Could not get trade history: {e}")
                # If we can't get history, be conservative and allow trading
                return False

            # Check against limit
            balance = account_info['balance']
            loss_pct = abs(self.daily_pnl) / balance if balance > 0 else 0

            return loss_pct >= self.max_daily_loss

        except Exception as e:
            self.logger.error(f"Error checking daily loss limit: {e}")
            return False  # Allow trading if we can't check

    def calculate_trailing_stop(self, current_price: float, position_type: str) -> float:
        """
        Calculate trailing stop loss price
        
        Args:
            current_price: Current market price
            position_type: Position type (BUY/SELL)
            
        Returns:
            Trailing stop price
        """
        trail_pct = self.config.get('trailing_stop_pct', 0.02)  # 2%
        
        if position_type == 'BUY':
            return current_price * (1 - trail_pct)
        else:
            return current_price * (1 + trail_pct)