#!/usr/bin/env python3
"""
Enhanced Technical Analysis Module
Includes advanced indicators, trend detection, and support/resistance levels
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class EnhancedTechnicalAnalysis:
    """Advanced technical analysis with multiple indicators and pattern recognition"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
        
    def calculate_stochastic(self, high: List[float], low: List[float], close: List[float], 
                           k_period: int = 14, d_period: int = 3) -> Tuple[float, float]:
        """Calculate Stochastic Oscillator (%K and %D)"""
        try:
            if len(close) < k_period:
                return 50.0, 50.0
                
            high_arr = np.array(high[-k_period:])
            low_arr = np.array(low[-k_period:])
            close_val = close[-1]
            
            highest_high = np.max(high_arr)
            lowest_low = np.min(low_arr)
            
            if highest_high == lowest_low:
                k_percent = 50.0
            else:
                k_percent = ((close_val - lowest_low) / (highest_high - lowest_low)) * 100
            
            # Simple D% calculation (SMA of %K)
            if len(close) >= k_period + d_period - 1:
                k_values = []
                for i in range(d_period):
                    idx = -(i + 1)
                    if abs(idx) <= len(close):
                        h = np.max(high[idx-k_period+1:idx+1])
                        l = np.min(low[idx-k_period+1:idx+1])
                        c = close[idx]
                        if h != l:
                            k_val = ((c - l) / (h - l)) * 100
                        else:
                            k_val = 50.0
                        k_values.append(k_val)
                d_percent = np.mean(k_values)
            else:
                d_percent = k_percent
                
            return k_percent, d_percent
            
        except Exception as e:
            logger.error(f"Stochastic calculation error: {e}")
            return 50.0, 50.0
    
    def calculate_williams_r(self, high: List[float], low: List[float], close: List[float], 
                           period: int = 14) -> float:
        """Calculate Williams %R"""
        try:
            if len(close) < period:
                return -50.0
                
            high_arr = np.array(high[-period:])
            low_arr = np.array(low[-period:])
            close_val = close[-1]
            
            highest_high = np.max(high_arr)
            lowest_low = np.min(low_arr)
            
            if highest_high == lowest_low:
                return -50.0
                
            williams_r = ((highest_high - close_val) / (highest_high - lowest_low)) * -100
            return williams_r
            
        except Exception as e:
            logger.error(f"Williams %R calculation error: {e}")
            return -50.0
    
    def calculate_cci(self, high: List[float], low: List[float], close: List[float], 
                     period: int = 20) -> float:
        """Calculate Commodity Channel Index (CCI)"""
        try:
            if len(close) < period:
                return 0.0
                
            # Calculate Typical Price
            typical_prices = []
            for i in range(len(close)):
                tp = (high[i] + low[i] + close[i]) / 3
                typical_prices.append(tp)
            
            # Get last 'period' typical prices
            recent_tp = typical_prices[-period:]
            sma_tp = np.mean(recent_tp)
            
            # Calculate Mean Deviation
            mean_deviation = np.mean([abs(tp - sma_tp) for tp in recent_tp])
            
            if mean_deviation == 0:
                return 0.0
                
            current_tp = typical_prices[-1]
            cci = (current_tp - sma_tp) / (0.015 * mean_deviation)
            
            return cci
            
        except Exception as e:
            logger.error(f"CCI calculation error: {e}")
            return 0.0
    
    def calculate_atr(self, high: List[float], low: List[float], close: List[float], 
                     period: int = 14) -> float:
        """Calculate Average True Range (ATR)"""
        try:
            if len(close) < 2:
                return 0.01
                
            true_ranges = []
            for i in range(1, len(close)):
                tr1 = high[i] - low[i]
                tr2 = abs(high[i] - close[i-1])
                tr3 = abs(low[i] - close[i-1])
                true_range = max(tr1, tr2, tr3)
                true_ranges.append(true_range)
            
            if len(true_ranges) < period:
                return np.mean(true_ranges) if true_ranges else 0.01
                
            atr = np.mean(true_ranges[-period:])
            return atr
            
        except Exception as e:
            logger.error(f"ATR calculation error: {e}")
            return 0.01
    
    def detect_trend(self, prices: List[float], short_period: int = 10, 
                    long_period: int = 50) -> Dict[str, any]:
        """Advanced trend detection using multiple methods"""
        try:
            if len(prices) < long_period:
                return {'trend': 'SIDEWAYS', 'strength': 0.5, 'confidence': 0.3}
            
            # Method 1: Moving Average Trend
            short_ma = np.mean(prices[-short_period:])
            long_ma = np.mean(prices[-long_period:])
            ma_trend = 'BULLISH' if short_ma > long_ma else 'BEARISH'
            
            # Method 2: Price Action Trend
            recent_prices = prices[-20:]
            price_slope = np.polyfit(range(len(recent_prices)), recent_prices, 1)[0]
            pa_trend = 'BULLISH' if price_slope > 0 else 'BEARISH'
            
            # Method 3: Higher Highs/Lower Lows
            highs = []
            lows = []
            for i in range(2, len(prices[-30:])):
                if prices[i-1] > prices[i-2] and prices[i-1] > prices[i]:
                    highs.append(prices[i-1])
                if prices[i-1] < prices[i-2] and prices[i-1] < prices[i]:
                    lows.append(prices[i-1])
            
            hh_ll_trend = 'SIDEWAYS'
            if len(highs) >= 2 and len(lows) >= 2:
                if highs[-1] > highs[-2] and lows[-1] > lows[-2]:
                    hh_ll_trend = 'BULLISH'
                elif highs[-1] < highs[-2] and lows[-1] < lows[-2]:
                    hh_ll_trend = 'BEARISH'
            
            # Combine trends
            trends = [ma_trend, pa_trend, hh_ll_trend]
            bullish_count = trends.count('BULLISH')
            bearish_count = trends.count('BEARISH')
            
            if bullish_count >= 2:
                final_trend = 'BULLISH'
                strength = bullish_count / 3
            elif bearish_count >= 2:
                final_trend = 'BEARISH'
                strength = bearish_count / 3
            else:
                final_trend = 'SIDEWAYS'
                strength = 0.5
            
            # Calculate confidence based on agreement
            confidence = max(bullish_count, bearish_count) / 3
            
            return {
                'trend': final_trend,
                'strength': strength,
                'confidence': confidence,
                'slope': price_slope,
                'ma_trend': ma_trend,
                'price_action_trend': pa_trend,
                'pattern_trend': hh_ll_trend
            }
            
        except Exception as e:
            logger.error(f"Trend detection error: {e}")
            return {'trend': 'SIDEWAYS', 'strength': 0.5, 'confidence': 0.3}
    
    def find_support_resistance(self, high: List[float], low: List[float], close: List[float], 
                               window: int = 20) -> Dict[str, List[float]]:
        """Find support and resistance levels using pivot points"""
        try:
            if len(close) < window * 2:
                current_price = close[-1]
                return {
                    'support_levels': [current_price * 0.98, current_price * 0.95],
                    'resistance_levels': [current_price * 1.02, current_price * 1.05],
                    'current_price': current_price
                }
            
            support_levels = []
            resistance_levels = []
            
            # Find pivot lows (support)
            for i in range(window, len(low) - window):
                is_pivot_low = True
                for j in range(i - window, i + window + 1):
                    if j != i and low[j] <= low[i]:
                        is_pivot_low = False
                        break
                if is_pivot_low:
                    support_levels.append(low[i])
            
            # Find pivot highs (resistance)
            for i in range(window, len(high) - window):
                is_pivot_high = True
                for j in range(i - window, i + window + 1):
                    if j != i and high[j] >= high[i]:
                        is_pivot_high = False
                        break
                if is_pivot_high:
                    resistance_levels.append(high[i])
            
            # Sort and get most relevant levels
            current_price = close[-1]
            
            # Filter support levels below current price
            valid_support = [s for s in support_levels if s < current_price]
            valid_support.sort(reverse=True)  # Closest to current price first
            
            # Filter resistance levels above current price
            valid_resistance = [r for r in resistance_levels if r > current_price]
            valid_resistance.sort()  # Closest to current price first
            
            # Take top 3 of each
            final_support = valid_support[:3] if valid_support else [current_price * 0.98]
            final_resistance = valid_resistance[:3] if valid_resistance else [current_price * 1.02]
            
            return {
                'support_levels': final_support,
                'resistance_levels': final_resistance,
                'current_price': current_price,
                'nearest_support': final_support[0] if final_support else current_price * 0.98,
                'nearest_resistance': final_resistance[0] if final_resistance else current_price * 1.02
            }
            
        except Exception as e:
            logger.error(f"Support/Resistance calculation error: {e}")
            current_price = close[-1] if close else 1.0
            return {
                'support_levels': [current_price * 0.98],
                'resistance_levels': [current_price * 1.02],
                'current_price': current_price,
                'nearest_support': current_price * 0.98,
                'nearest_resistance': current_price * 1.02
            }
