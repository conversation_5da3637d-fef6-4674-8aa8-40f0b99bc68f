# src/data/news.py
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from ..utils.logger import get_logger

class NewsDataCollector:
    def __init__(self, api_key: str, sources: List[str] = None):
        self.api_key = api_key
        self.sources = sources or ['Reuters', 'Bloomberg', 'CNBC']
        self.logger = get_logger('news')
        self.cache = {}
        
    def get_news(
        self,
        symbol: str,
        lookback_hours: int = 24,
        min_sentiment: float = None,
        max_sentiment: float = None
    ) -> List[Dict]:
        """Get news articles for a symbol with optional sentiment filtering"""
        cache_key = f"{symbol}_{lookback_hours}"
        if cache_key in self.cache:
            return self.cache[cache_key]
            
        end_date = datetime.now()
        start_date = end_date - timedelta(hours=lookback_hours)
        
        try:
            params = {
                'q': symbol,
                'from': start_date.strftime('%Y-%m-%dT%H:%M:%S'),
                'to': end_date.strftime('%Y-%m-%dT%H:%M:%S'),
                'sortBy': 'publishedAt',
                'apiKey': self.api_key,
                'sources': ','.join(self.sources),
                'language': 'en'
            }
            
            response = requests.get(
                'https://newsapi.org/v2/everything',
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            articles = response.json().get('articles', [])
            
            # Add sentiment analysis
            analyzed = []
            for article in articles:
                sentiment = self._analyze_sentiment(article)
                article['sentiment'] = sentiment
                
                # Apply filters
                if min_sentiment is not None and sentiment['score'] < min_sentiment:
                    continue
                if max_sentiment is not None and sentiment['score'] > max_sentiment:
                    continue
                    
                analyzed.append(article)
                
            self.cache[cache_key] = analyzed
            return analyzed
            
        except Exception as e:
            self.logger.error(f"Error fetching news: {str(e)}")
            return []
            
    def _analyze_sentiment(self, article: Dict) -> Dict:
        """Perform sentiment analysis on a news article"""
        # This would use a proper NLP library in production
        text = f"{article['title']}. {article['description']}"
        positive_words = ['bullish', 'gain', 'rise', 'positive', 'strong', 'buy']
        negative_words = ['bearish', 'drop', 'fall', 'negative', 'weak', 'sell']
        
        score = 0.5  # Neutral baseline
        for word in positive_words:
            if word in text.lower():
                score += 0.1
        for word in negative_words:
            if word in text.lower():
                score -= 0.1
                
        score = max(0, min(1, score))
        
        return {
            'score': score,
            'sentiment': 'positive' if score > 0.6 else 'negative' if score < 0.4 else 'neutral'
        }
        
    def clear_cache(self):
        """Clear the news cache"""
        self.cache = {}