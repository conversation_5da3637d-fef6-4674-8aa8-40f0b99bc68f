#!/usr/bin/env python3
"""
Emergency Close All Trades Script
Run this to immediately close all open positions
"""

import os
import sys
import json
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

class EmergencyCloser:
    """Emergency position closer"""
    
    def __init__(self):
        self.mt4_data_path = None
        self.find_mt4_path()
        
    def find_mt4_path(self):
        """Find MT4 data path"""
        mt4_paths = [
            os.path.expanduser("~\\AppData\\Roaming\\MetaQuotes\\Terminal"),
            os.path.join(os.getcwd(), "mt4_files")
        ]
        
        for path in mt4_paths:
            if os.path.exists(path):
                if "MetaQuotes" in path:
                    for folder in os.listdir(path):
                        terminal_path = os.path.join(path, folder, "MQL4", "Files")
                        if os.path.exists(terminal_path):
                            self.mt4_data_path = terminal_path
                            break
                else:
                    self.mt4_data_path = path
                break
        
        if not self.mt4_data_path:
            self.mt4_data_path = os.path.join(os.getcwd(), "mt4_files")
            os.makedirs(self.mt4_data_path, exist_ok=True)
    
    def send_close_all_command(self):
        """Send close all positions command to MT4"""
        try:
            command = {
                "command": "close_all_positions",
                "timestamp": datetime.now().isoformat()
            }
            
            # Write command file
            command_file = os.path.join(self.mt4_data_path, "python_commands.json")
            with open(command_file, 'w') as f:
                json.dump(command, f)
            
            logger.info("✅ Close all positions command sent to MT4")
            logger.info(f"📁 Command file: {command_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending close command: {e}")
            return False
    
    def close_all_positions(self):
        """Main function to close all positions"""
        logger.info("🚨 EMERGENCY CLOSE ALL POSITIONS")
        logger.info("=" * 50)
        
        # Send close command
        if self.send_close_all_command():
            logger.info("✅ Close all command executed successfully")
            logger.info("⏳ Positions should close within 30 seconds")
            logger.info("📊 Check your MT4 terminal to confirm")
        else:
            logger.error("❌ Failed to send close command")
            logger.info("🔧 Try closing positions manually in MT4")
        
        logger.info("=" * 50)

def main():
    """Main execution"""
    print("\n🚨 EMERGENCY CLOSE ALL TRADES 🚨")
    print("This will attempt to close ALL open positions!")
    
    confirm = input("\nAre you sure? Type 'YES' to continue: ")
    
    if confirm.upper() == 'YES':
        closer = EmergencyCloser()
        closer.close_all_positions()
    else:
        print("❌ Operation cancelled")

if __name__ == "__main__":
    main()
