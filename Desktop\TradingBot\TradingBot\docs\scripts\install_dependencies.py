# scripts/install_dependencies.py
import os
import subprocess
import sys

def install_requirements():
    """Install all Python dependencies"""
    requirements = [
        'MetaTrader4',
        'pandas',
        'numpy',
        'ta-lib',
        'python-dotenv',
        'requests',
        'schedule',
        'textblob',
        'nltk',
        'pytest'
    ]
    
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"Successfully installed {package}")
        except subprocess.CalledProcessError:
            print(f"Failed to install {package}")
            
if __name__ == '__main__':
    install_requirements()
