# src/core/portfolio_manager.py
from typing import Dict, List, Optional
from datetime import datetime
from ..brokers import IBroker

class PortfolioManager:
    def __init__(self, broker: IBroker):
        """
        Initialize portfolio manager with broker connection
        
        Args:
            broker: Initialized broker instance implementing IBroker interface
        """
        self.broker = broker
        self.positions = {}
        self.history = []
        self.last_update = None

    def update(self):
        """Update portfolio information from broker"""
        self.positions = self._get_current_positions()
        self.last_update = datetime.now()

    def _get_current_positions(self) -> Dict[str, Dict]:
        """Get current positions from broker and format them"""
        positions = {}
        raw_positions = self.broker.get_all_positions()
        
        for pos in raw_positions:
            positions[pos['symbol']] = {
                'ticket': pos['ticket'],
                'type': pos['type'],
                'volume': pos['volume'],
                'entry_price': pos['open_price'],
                'current_price': pos['current_price'],
                'stop_loss': pos['stop_loss'],
                'take_profit': pos['take_profit'],
                'profit': pos['profit'],
                'time': pos['open_time'],
                'comment': pos['comment']
            }
            
        return positions

    def get_position(self, symbol: str) -> Optional[Dict]:
        """
        Get specific position by symbol
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Position dictionary or None if not found
        """
        return self.positions.get(symbol)

    def get_portfolio_summary(self) -> Dict:
        """
        Get summary of portfolio status
        
        Returns:
            Dictionary with portfolio metrics
        """
        total_profit = sum(p['profit'] for p in self.positions.values())
        total_risk = sum(
            abs(p['entry_price'] - p['stop_loss']) * p['volume'] 
            for p in self.positions.values() 
            if p['stop_loss'] is not None
        )
        
        account = self.broker.get_account_info()
        
        return {
            'balance': account['balance'],
            'equity': account['equity'],
            'margin': account['margin'],
            'free_margin': account['margin_free'],
            'positions_count': len(self.positions),
            'total_profit': total_profit,
            'total_risk': total_risk,
            'margin_level': account['margin_level']
        }

    def get_exposure(self) -> Dict[str, float]:
        """
        Calculate exposure by asset class
        
        Returns:
            Dictionary with exposure percentages
        """
        account = self.broker.get_account_info()
        equity = account['equity']
        
        exposure = {}
        for symbol, pos in self.positions.items():
            asset_class = self._get_asset_class(symbol)
            notional = pos['volume'] * pos['current_price']
            exposure[asset_class] = exposure.get(asset_class, 0) + (notional / equity)
            
        return exposure

    def _get_asset_class(self, symbol: str) -> str:
        """Categorize symbol into asset class"""
        if 'XAU' in symbol:
            return 'GOLD'
        elif 'NAS' in symbol:
            return 'NASDAQ'
        elif 'US30' in symbol:
            return 'DOW'
        elif 'SPX' in symbol:
            return 'SP500'
        else:
            return 'FOREX'

    def get_performance_metrics(self, period: str = 'D') -> Dict:
        """
        Calculate performance metrics for a given period
        
        Args:
            period: Time period ('D'aily, 'W'weekly, 'M'onthly)
            
        Returns:
            Dictionary with performance metrics
        """
        end = datetime.now()
        if period == 'D':
            start = end.replace(hour=0, minute=0, second=0)
        elif period == 'W':
            start = end - timedelta(days=end.weekday())
            start = start.replace(hour=0, minute=0, second=0)
        elif period == 'M':
            start = end.replace(day=1, hour=0, minute=0, second=0)
        else:
            start = end - timedelta(days=1)
            
        trades = self.broker.get_trade_history(start, end)
        
        if not trades:
            return {
                'period': period,
                'start': start,
                'end': end,
                'trades_count': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'avg_win': 0,
                'avg_loss': 0
            }
            
        winning_trades = [t for t in trades if t['profit'] > 0]
        losing_trades = [t for t in trades if t['profit'] <= 0]
        
        win_rate = len(winning_trades) / len(trades)
        gross_profit = sum(t['profit'] for t in winning_trades)
        gross_loss = abs(sum(t['profit'] for t in losing_trades))
        
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        avg_win = gross_profit / len(winning_trades) if winning_trades else 0
        avg_loss = gross_loss / len(losing_trades) if losing_trades else 0
        
        return {
            'period': period,
            'start': start,
            'end': end,
            'trades_count': len(trades),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'net_profit': gross_profit - gross_loss
        }