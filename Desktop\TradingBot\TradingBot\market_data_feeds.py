#!/usr/bin/env python3
"""
Real Market Data Integration
Alpha Vantage, Yahoo Finance, Economic Calendar, News Sentiment
"""

import requests
import yfinance as yf
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
import time
from textblob import TextBlob
import feedparser

logger = logging.getLogger(__name__)

class MarketDataFeeds:
    """Real-time market data integration from multiple sources"""
    
    def __init__(self, alpha_vantage_key: str = None):
        self.alpha_vantage_key = alpha_vantage_key or "demo"  # Replace with real key
        self.base_url_av = "https://www.alphavantage.co/query"
        self.cache = {}
        self.cache_timeout = 60  # 1 minute cache
        
        # Symbol mapping for different data sources
        self.symbol_mapping = {
            'XAUUSD': {'yahoo': 'GC=F', 'av': 'XAU/USD'},
            'EURUSD': {'yahoo': 'EURUSD=X', 'av': 'EUR/USD'},
            'GBPUSD': {'yahoo': 'GBPUSD=X', 'av': 'GBP/USD'},
            'NAS100': {'yahoo': '^IXIC', 'av': 'NDAQ'},
            'US30': {'yahoo': '^DJI', 'av': 'DJI'}
        }
    
    def get_real_time_price(self, symbol: str) -> Dict[str, float]:
        """Get real-time price data from Yahoo Finance"""
        try:
            cache_key = f"price_{symbol}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            yahoo_symbol = self.symbol_mapping.get(symbol, {}).get('yahoo', symbol)
            
            ticker = yf.Ticker(yahoo_symbol)
            info = ticker.info
            
            # Get current price data
            hist = ticker.history(period="1d", interval="1m")
            if hist.empty:
                raise Exception(f"No data for {symbol}")
            
            latest = hist.iloc[-1]
            
            price_data = {
                'bid': float(latest['Close']),
                'ask': float(latest['Close']) * 1.0001,  # Small spread simulation
                'high': float(latest['High']),
                'low': float(latest['Low']),
                'volume': int(latest['Volume']) if 'Volume' in latest else 0,
                'timestamp': datetime.now().isoformat()
            }
            
            self._cache_data(cache_key, price_data)
            return price_data
            
        except Exception as e:
            logger.error(f"Error getting real-time price for {symbol}: {e}")
            return self._get_fallback_price(symbol)
    
    def get_historical_data(self, symbol: str, period: str = "1mo", 
                           interval: str = "1h") -> pd.DataFrame:
        """Get historical price data"""
        try:
            cache_key = f"hist_{symbol}_{period}_{interval}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            yahoo_symbol = self.symbol_mapping.get(symbol, {}).get('yahoo', symbol)
            
            ticker = yf.Ticker(yahoo_symbol)
            hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                raise Exception(f"No historical data for {symbol}")
            
            # Add technical indicators
            hist['SMA_20'] = hist['Close'].rolling(window=20).mean()
            hist['SMA_50'] = hist['Close'].rolling(window=50).mean()
            hist['RSI'] = self._calculate_rsi(hist['Close'].tolist())
            
            self._cache_data(cache_key, hist, timeout=300)  # 5 min cache for historical
            return hist
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def get_economic_calendar(self, days_ahead: int = 7) -> List[Dict]:
        """Get economic calendar events (simplified version)"""
        try:
            cache_key = f"econ_calendar_{days_ahead}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            # This is a simplified version - in production, use a real economic calendar API
            # like ForexFactory, Investing.com API, or similar
            
            events = [
                {
                    'date': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
                    'time': '14:30',
                    'currency': 'USD',
                    'event': 'Non-Farm Payrolls',
                    'impact': 'High',
                    'forecast': '200K',
                    'previous': '180K'
                },
                {
                    'date': (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d'),
                    'time': '12:30',
                    'currency': 'EUR',
                    'event': 'ECB Interest Rate Decision',
                    'impact': 'High',
                    'forecast': '4.50%',
                    'previous': '4.50%'
                },
                {
                    'date': (datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d'),
                    'time': '09:30',
                    'currency': 'GBP',
                    'event': 'GDP Growth Rate',
                    'impact': 'Medium',
                    'forecast': '0.2%',
                    'previous': '0.1%'
                }
            ]
            
            self._cache_data(cache_key, events, timeout=3600)  # 1 hour cache
            return events
            
        except Exception as e:
            logger.error(f"Error getting economic calendar: {e}")
            return []
    
    def get_news_sentiment(self, symbol: str, limit: int = 10) -> Dict[str, any]:
        """Get news sentiment analysis for a symbol"""
        try:
            cache_key = f"news_{symbol}_{limit}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            # Get news from multiple sources
            news_items = []
            
            # RSS feeds for financial news
            feeds = [
                'https://feeds.finance.yahoo.com/rss/2.0/headline',
                'https://www.reuters.com/markets/currencies/rss',
                'https://www.forexfactory.com/rss.php'
            ]
            
            for feed_url in feeds:
                try:
                    feed = feedparser.parse(feed_url)
                    for entry in feed.entries[:5]:  # Limit per feed
                        news_items.append({
                            'title': entry.title,
                            'summary': entry.get('summary', ''),
                            'published': entry.get('published', ''),
                            'link': entry.get('link', '')
                        })
                except:
                    continue
            
            # Analyze sentiment
            sentiments = []
            for item in news_items[:limit]:
                text = f"{item['title']} {item['summary']}"
                blob = TextBlob(text)
                sentiment = blob.sentiment.polarity  # -1 to 1
                sentiments.append({
                    'title': item['title'],
                    'sentiment': sentiment,
                    'published': item['published']
                })
            
            # Calculate overall sentiment
            if sentiments:
                avg_sentiment = np.mean([s['sentiment'] for s in sentiments])
                sentiment_strength = abs(avg_sentiment)
                sentiment_direction = 'BULLISH' if avg_sentiment > 0.1 else 'BEARISH' if avg_sentiment < -0.1 else 'NEUTRAL'
            else:
                avg_sentiment = 0.0
                sentiment_strength = 0.0
                sentiment_direction = 'NEUTRAL'
            
            result = {
                'overall_sentiment': avg_sentiment,
                'sentiment_strength': sentiment_strength,
                'sentiment_direction': sentiment_direction,
                'news_count': len(sentiments),
                'individual_sentiments': sentiments[:5]  # Top 5
            }
            
            self._cache_data(cache_key, result, timeout=900)  # 15 min cache
            return result
            
        except Exception as e:
            logger.error(f"Error getting news sentiment for {symbol}: {e}")
            return {
                'overall_sentiment': 0.0,
                'sentiment_strength': 0.0,
                'sentiment_direction': 'NEUTRAL',
                'news_count': 0,
                'individual_sentiments': []
            }
    
    def get_volume_analysis(self, symbol: str) -> Dict[str, any]:
        """Get volume analysis for a symbol"""
        try:
            cache_key = f"volume_{symbol}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            # Get recent volume data
            hist = self.get_historical_data(symbol, period="5d", interval="1h")
            
            if hist.empty or 'Volume' not in hist.columns:
                return {'volume_trend': 'UNKNOWN', 'volume_ratio': 1.0, 'avg_volume': 0}
            
            volumes = hist['Volume'].dropna()
            if len(volumes) < 10:
                return {'volume_trend': 'UNKNOWN', 'volume_ratio': 1.0, 'avg_volume': 0}
            
            current_volume = volumes.iloc[-1]
            avg_volume = volumes.mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # Determine volume trend
            recent_avg = volumes.tail(10).mean()
            older_avg = volumes.head(len(volumes) - 10).mean()
            
            if recent_avg > older_avg * 1.2:
                volume_trend = 'INCREASING'
            elif recent_avg < older_avg * 0.8:
                volume_trend = 'DECREASING'
            else:
                volume_trend = 'STABLE'
            
            result = {
                'volume_trend': volume_trend,
                'volume_ratio': volume_ratio,
                'current_volume': int(current_volume),
                'avg_volume': int(avg_volume),
                'volume_spike': volume_ratio > 2.0
            }
            
            self._cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Error getting volume analysis for {symbol}: {e}")
            return {'volume_trend': 'UNKNOWN', 'volume_ratio': 1.0, 'avg_volume': 0}
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> List[float]:
        """Calculate RSI for price series"""
        try:
            if len(prices) < period + 1:
                return [50.0] * len(prices)
            
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            rsi_values = []
            for i in range(len(prices)):
                if i < period:
                    rsi_values.append(50.0)
                else:
                    avg_gain = np.mean(gains[i-period:i])
                    avg_loss = np.mean(losses[i-period:i])
                    
                    if avg_loss == 0:
                        rsi = 100.0
                    else:
                        rs = avg_gain / avg_loss
                        rsi = 100 - (100 / (1 + rs))
                    
                    rsi_values.append(rsi)
            
            return rsi_values
            
        except:
            return [50.0] * len(prices)
    
    def _is_cached(self, key: str) -> bool:
        """Check if data is cached and still valid"""
        if key not in self.cache:
            return False
        
        cache_time = self.cache[key]['timestamp']
        timeout = self.cache[key].get('timeout', self.cache_timeout)
        
        return (time.time() - cache_time) < timeout
    
    def _cache_data(self, key: str, data: any, timeout: int = None) -> None:
        """Cache data with timestamp"""
        self.cache[key] = {
            'data': data,
            'timestamp': time.time(),
            'timeout': timeout or self.cache_timeout
        }
    
    def _get_fallback_price(self, symbol: str) -> Dict[str, float]:
        """Get fallback price data when real data fails"""
        base_prices = {
            'XAUUSD': 2000.0,
            'EURUSD': 1.085,
            'GBPUSD': 1.265,
            'NAS100': 15000.0,
            'US30': 35000.0
        }
        
        base_price = base_prices.get(symbol, 1.0)
        
        return {
            'bid': base_price,
            'ask': base_price * 1.0001,
            'high': base_price * 1.002,
            'low': base_price * 0.998,
            'volume': 1000,
            'timestamp': datetime.now().isoformat()
        }
