# src/strategies/trend_following.py
import pandas as pd
import talib
from typing import Dict, Any
from .base_strategy import BaseStrategy

class TrendFollowingStrategy(BaseStrategy):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.ema_fast = config.get('ema_fast', 50)
        self.ema_slow = config.get('ema_slow', 200)
        self.adx_threshold = config.get('adx_threshold', 25)
        self.atr_period = config.get('atr_period', 14)
        
    def analyze(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Perform trend following analysis"""
        close = data['close'].values
        high = data['high'].values
        low = data['low'].values
        
        # Calculate indicators
        ema_fast = talib.EMA(close, timeperiod=self.ema_fast)
        ema_slow = talib.EMA(close, timeperiod=self.ema_slow)
        adx = talib.ADX(high, low, close, timeperiod=14)
        atr = talib.ATR(high, low, close, timeperiod=self.atr_period)
        
        # Current values
        current_ema_fast = ema_fast[-1]
        current_ema_slow = ema_slow[-1]
        current_adx = adx[-1]
        current_atr = atr[-1]
        current_close = close[-1]
        
        # Generate signals
        signals = {
            'ema_fast': current_ema_fast,
            'ema_slow': current_ema_slow,
            'ema_cross': current_ema_fast > current_ema_slow,
            'adx': current_adx,
            'atr': current_atr,
            'trend_strength': 'strong' if current_adx > self.adx_threshold else 'weak'
        }
        
        return {
            'indicators': {
                'ema_fast': ema_fast,
                'ema_slow': ema_slow,
                'adx': adx,
                'atr': atr
            },
            'signals': signals,
            'current_price': current_close
        }
        
    def get_signal(self, analysis: Dict[str, Any]) -> str:
        """Generate trading signal"""
        signals = analysis['signals']
        
        # Strong trend conditions
        if signals['trend_strength'] == 'strong':
            if signals['ema_cross']:
                return 'BUY'
            else:
                return 'SELL'
                
        return 'HOLD'
