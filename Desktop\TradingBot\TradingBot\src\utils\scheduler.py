# src/utils/scheduler.py
import schedule
import time
import threading
from typing import Callable, Dict, Optional
from datetime import time as dt_time, datetime

class TradingScheduler:
    def __init__(self):
        self.jobs = {}
        self.running = False
        self.thread = None
        
    def add_daily_job(
        self,
        name: str,
        func: Callable,
        hour: int,
        minute: int = 0,
        args: tuple = (),
        kwargs: Dict = None
    ):
        """Add a job to run daily at specific time"""
        if kwargs is None:
            kwargs = {}
            
        job = schedule.every().day.at(f"{hour:02d}:{minute:02d}").do(func, *args, **kwargs)
        self.jobs[name] = job
        
    def add_intraday_job(
        self,
        name: str,
        func: Callable,
        interval_minutes: int,
        args: tuple = (),
        kwargs: Dict = None
    ):
        """Add a job to run at regular intervals"""
        if kwargs is None:
            kwargs = {}
            
        job = schedule.every(interval_minutes).minutes.do(func, *args, **kwargs)
        self.jobs[name] = job
        
    def remove_job(self, name: str):
        """Remove a scheduled job"""
        if name in self.jobs:
            schedule.cancel_job(self.jobs[name])
            del self.jobs[name]
            
    def start(self):
        """Start the scheduler in a background thread"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run, daemon=True)
        self.thread.start()
        
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        if self.thread:
            self.thread.join()
            
    def _run(self):
        """Run the scheduler loop"""
        while self.running:
            schedule.run_pending()
            time.sleep(1)
            
    def get_next_run(self, name: str) -> Optional[datetime]:
        """Get next run time for a job"""
        if name in self.jobs:
            return self.jobs[name].next_run
        return None
