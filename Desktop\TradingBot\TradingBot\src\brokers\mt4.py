# src/brokers/mt4.py
import MetaTrader4 as mt4
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from .broker_interface import IBroker


class TradingError(Exception):
    """Custom exception for trading errors"""
    pass


class MT4Broker(IBroker):
    """MetaTrader 4 broker implementation"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.connected = False

        # Initialize and connect to MT4
        if not self.connect():
            raise ConnectionError("Cannot connect to MT4")

    def connect(self) -> bool:
        """Connect to MetaTrader 4"""
        try:
            # Initialize MT4 connection
            if not mt4.initialize():
                self.logger.error("Failed to initialize MT4")
                return False

            # Login with credentials
            login_result = mt4.login(
                login=self.config['login'],
                password=self.config['password'],
                server=self.config['server'],
                timeout=self.config.get('timeout', 60000),
                portable=self.config.get('portable', False)
            )

            if not login_result:
                error_code = mt4.last_error()
                self.logger.error(f"MT4 login failed. Error code: {error_code}")
                return False

            self.connected = True
            self.logger.info(f"Successfully connected to MT4 server: {self.config['server']}")
            self.logger.info(f"Account: {self.config['login']}")

            return True

        except Exception as e:
            self.logger.error(f"Error connecting to MT4: {e}")
            return False

    def disconnect(self) -> bool:
        """Disconnect from MetaTrader 4"""
        try:
            mt4.shutdown()
            self.connected = False
            self.logger.info("Disconnected from MT4")
            return True
        except Exception as e:
            self.logger.error(f"Error disconnecting from MT4: {e}")
            return False

    def is_connected(self) -> bool:
        """Check if connection to MT4 is active"""
        return self.connected and mt4.terminal_info() is not None

    def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        try:
            account_info = mt4.account_info()
            if account_info is None:
                raise TradingError("Failed to get account info")

            return {
                'login': account_info.login,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'margin_free': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'currency': account_info.currency,
                'server': account_info.server,
                'leverage': account_info.leverage
            }
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            raise TradingError(f"Failed to get account info: {e}")

    def get_current_price(self, symbol: str) -> Dict[str, float]:
        """Get current bid/ask prices for a symbol"""
        try:
            tick = mt4.symbol_info_tick(symbol)
            if tick is None:
                raise TradingError(f"Failed to get price for {symbol}")

            return {
                'bid': tick.bid,
                'ask': tick.ask,
                'time': tick.time
            }
        except Exception as e:
            self.logger.error(f"Error getting price for {symbol}: {e}")
            raise TradingError(f"Failed to get price for {symbol}: {e}")

    def place_order(self, symbol: str, order_type: str, volume: float,
                   stop_loss: Optional[float] = None, take_profit: Optional[float] = None,
                   comment: str = "", **kwargs) -> Dict[str, Any]:
        """Place a new order"""
        try:
            # Get current price
            tick = mt4.symbol_info_tick(symbol)
            if tick is None:
                raise TradingError(f"Cannot get price for {symbol}")

            # Determine order type and price
            if order_type.upper() == 'BUY':
                mt4_order_type = mt4.ORDER_BUY
                price = tick.ask
            elif order_type.upper() == 'SELL':
                mt4_order_type = mt4.ORDER_SELL
                price = tick.bid
            else:
                raise TradingError(f"Unsupported order type: {order_type}")

            # Prepare order request
            request = {
                "action": mt4.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": mt4_order_type,
                "price": price,
                "deviation": self.config.get('deviation', 20),
                "magic": self.config.get('magic_number', 234000),
                "comment": comment,
                "type_time": mt4.ORDER_TIME_GTC,
                "type_filling": mt4.ORDER_FILLING_FOK,
            }

            # Add stop loss and take profit if provided
            if stop_loss is not None:
                request["sl"] = stop_loss
            if take_profit is not None:
                request["tp"] = take_profit

            # Send order
            result = mt4.order_send(request)

            if result.retcode != mt4.TRADE_RETCODE_DONE:
                raise TradingError(f"Order failed: {result.comment} (Code: {result.retcode})")

            self.logger.info(f"Order placed successfully: {symbol} {order_type} {volume} lots")

            return {
                'ticket': result.order,
                'retcode': result.retcode,
                'deal': result.deal,
                'order': result.order,
                'volume': result.volume,
                'price': result.price,
                'comment': result.comment
            }

        except Exception as e:
            self.logger.error(f"Error placing order: {e}")
            raise TradingError(f"Failed to place order: {e}")

    def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current position for a specific symbol"""
        try:
            positions = mt4.positions_get(symbol=symbol)
            if positions is None or len(positions) == 0:
                return None

            # Return the first position for the symbol
            pos = positions[0]
            return {
                'ticket': pos.ticket,
                'symbol': pos.symbol,
                'volume': pos.volume,
                'type': 'BUY' if pos.type == mt4.POSITION_TYPE_BUY else 'SELL',
                'price_open': pos.price_open,
                'price_current': pos.price_current,
                'profit': pos.profit,
                'swap': pos.swap,
                'comment': pos.comment,
                'time': pos.time
            }
        except Exception as e:
            self.logger.error(f"Error getting position for {symbol}: {e}")
            return None

    def get_all_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions"""
        try:
            positions = mt4.positions_get()
            if positions is None:
                return []

            result = []
            for pos in positions:
                result.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'volume': pos.volume,
                    'type': 'BUY' if pos.type == mt4.POSITION_TYPE_BUY else 'SELL',
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'profit': pos.profit,
                    'swap': pos.swap,
                    'comment': pos.comment,
                    'time': pos.time
                })
            return result
        except Exception as e:
            self.logger.error(f"Error getting all positions: {e}")
            return []

    def get_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Get pending orders"""
        try:
            if symbol:
                orders = mt4.orders_get(symbol=symbol)
            else:
                orders = mt4.orders_get()

            if orders is None:
                return []

            result = []
            for order in orders:
                result.append({
                    'ticket': order.ticket,
                    'symbol': order.symbol,
                    'volume': order.volume_initial,
                    'type': self._get_order_type_string(order.type),
                    'price_open': order.price_open,
                    'sl': order.sl,
                    'tp': order.tp,
                    'comment': order.comment,
                    'time_setup': order.time_setup
                })
            return result
        except Exception as e:
            self.logger.error(f"Error getting orders: {e}")
            return []

    def close_position(self, ticket: int) -> bool:
        """Close a position"""
        try:
            # Get position info
            positions = mt4.positions_get(ticket=ticket)
            if positions is None or len(positions) == 0:
                raise TradingError(f"Position {ticket} not found")

            position = positions[0]

            # Prepare close request
            request = {
                "action": mt4.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": mt4.ORDER_SELL if position.type == mt4.POSITION_TYPE_BUY else mt4.ORDER_BUY,
                "position": ticket,
                "deviation": self.config.get('deviation', 20),
                "magic": self.config.get('magic_number', 234000),
                "comment": "Close position",
                "type_time": mt4.ORDER_TIME_GTC,
                "type_filling": mt4.ORDER_FILLING_FOK,
            }

            # Get current price
            tick = mt4.symbol_info_tick(position.symbol)
            if tick is None:
                raise TradingError(f"Cannot get price for {position.symbol}")

            request["price"] = tick.bid if position.type == mt4.POSITION_TYPE_BUY else tick.ask

            # Send close request
            result = mt4.order_send(request)

            if result.retcode != mt4.TRADE_RETCODE_DONE:
                raise TradingError(f"Failed to close position: {result.comment}")

            self.logger.info(f"Position {ticket} closed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error closing position {ticket}: {e}")
            return False

    def modify_position(self, ticket: int, stop_loss: Optional[float] = None,
                       take_profit: Optional[float] = None) -> bool:
        """Modify position stop loss and take profit"""
        try:
            # Get position info
            positions = mt4.positions_get(ticket=ticket)
            if positions is None or len(positions) == 0:
                raise TradingError(f"Position {ticket} not found")

            position = positions[0]

            # Prepare modification request
            request = {
                "action": mt4.TRADE_ACTION_SLTP,
                "symbol": position.symbol,
                "position": ticket,
                "magic": self.config.get('magic_number', 234000),
            }

            if stop_loss is not None:
                request["sl"] = stop_loss
            if take_profit is not None:
                request["tp"] = take_profit

            # Send modification request
            result = mt4.order_send(request)

            if result.retcode != mt4.TRADE_RETCODE_DONE:
                raise TradingError(f"Failed to modify position: {result.comment}")

            self.logger.info(f"Position {ticket} modified successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error modifying position {ticket}: {e}")
            return False

    def cancel_order(self, ticket: int) -> bool:
        """Cancel a pending order"""
        try:
            request = {
                "action": mt4.TRADE_ACTION_REMOVE,
                "order": ticket,
            }

            result = mt4.order_send(request)

            if result.retcode != mt4.TRADE_RETCODE_DONE:
                raise TradingError(f"Failed to cancel order: {result.comment}")

            self.logger.info(f"Order {ticket} cancelled successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error cancelling order {ticket}: {e}")
            return False

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get information about a trading symbol"""
        try:
            symbol_info = mt4.symbol_info(symbol)
            if symbol_info is None:
                raise TradingError(f"Symbol {symbol} not found")

            return {
                'name': symbol_info.name,
                'digits': symbol_info.digits,
                'point': symbol_info.point,
                'spread': symbol_info.spread,
                'volume_min': symbol_info.volume_min,
                'volume_max': symbol_info.volume_max,
                'volume_step': symbol_info.volume_step,
                'contract_size': symbol_info.trade_contract_size,
                'margin_initial': symbol_info.margin_initial,
                'currency_base': symbol_info.currency_base,
                'currency_profit': symbol_info.currency_profit,
                'currency_margin': symbol_info.currency_margin
            }
        except Exception as e:
            self.logger.error(f"Error getting symbol info for {symbol}: {e}")
            raise TradingError(f"Failed to get symbol info for {symbol}: {e}")

    def get_historical_data(self, symbol: str, timeframe: str,
                           start: Optional[datetime] = None, end: Optional[datetime] = None,
                           count: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get historical price data"""
        try:
            # Convert timeframe string to MT4 constant
            tf_map = {
                'M1': mt4.TIMEFRAME_M1,
                'M5': mt4.TIMEFRAME_M5,
                'M15': mt4.TIMEFRAME_M15,
                'M30': mt4.TIMEFRAME_M30,
                'H1': mt4.TIMEFRAME_H1,
                'H4': mt4.TIMEFRAME_H4,
                'D1': mt4.TIMEFRAME_D1,
                'W1': mt4.TIMEFRAME_W1,
                'MN1': mt4.TIMEFRAME_MN1
            }

            mt4_timeframe = tf_map.get(timeframe)
            if mt4_timeframe is None:
                raise TradingError(f"Unsupported timeframe: {timeframe}")

            # Get rates
            if count:
                rates = mt4.copy_rates_from_pos(symbol, mt4_timeframe, 0, count)
            elif start and end:
                rates = mt4.copy_rates_range(symbol, mt4_timeframe, start, end)
            else:
                # Default to last 100 bars
                rates = mt4.copy_rates_from_pos(symbol, mt4_timeframe, 0, 100)

            if rates is None:
                return []

            result = []
            for rate in rates:
                result.append({
                    'time': datetime.fromtimestamp(rate['time']),
                    'open': rate['open'],
                    'high': rate['high'],
                    'low': rate['low'],
                    'close': rate['close'],
                    'volume': rate['tick_volume']
                })

            return result

        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return []

    def get_market_depth(self, symbol: str) -> Dict[str, List[Dict[str, float]]]:
        """Get market depth (order book) data"""
        # MT4 doesn't provide market depth data like MT5
        # Return empty structure for compatibility
        return {'bids': [], 'asks': []}

    def get_server_time(self) -> datetime:
        """Get broker server time"""
        try:
            terminal_info = mt4.terminal_info()
            if terminal_info is None:
                raise TradingError("Cannot get terminal info")
            return datetime.now()  # MT4 doesn't provide server time directly
        except Exception as e:
            self.logger.error(f"Error getting server time: {e}")
            return datetime.now()

    def get_margin_requirements(self, symbol: str, volume: float) -> Dict[str, float]:
        """Calculate margin requirements for a potential trade"""
        try:
            symbol_info = mt4.symbol_info(symbol)
            if symbol_info is None:
                raise TradingError(f"Symbol {symbol} not found")

            # Calculate margin requirement
            margin = volume * symbol_info.trade_contract_size * symbol_info.margin_initial

            return {
                'margin_required': margin,
                'margin_currency': symbol_info.currency_margin
            }
        except Exception as e:
            self.logger.error(f"Error calculating margin for {symbol}: {e}")
            return {'margin_required': 0.0, 'margin_currency': 'USD'}

    def get_available_symbols(self) -> List[str]:
        """Get list of available trading symbols"""
        try:
            symbols = mt4.symbols_get()
            if symbols is None:
                return []
            return [symbol.name for symbol in symbols]
        except Exception as e:
            self.logger.error(f"Error getting available symbols: {e}")
            return []

    def get_trade_history(self, start: datetime, end: datetime,
                         symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get historical trade data"""
        try:
            if symbol:
                deals = mt4.history_deals_get(start, end, group=symbol)
            else:
                deals = mt4.history_deals_get(start, end)

            if deals is None:
                return []

            result = []
            for deal in deals:
                result.append({
                    'ticket': deal.ticket,
                    'order': deal.order,
                    'symbol': deal.symbol,
                    'type': 'BUY' if deal.type == mt4.DEAL_TYPE_BUY else 'SELL',
                    'volume': deal.volume,
                    'price': deal.price,
                    'profit': deal.profit,
                    'swap': deal.swap,
                    'commission': deal.commission,
                    'time': datetime.fromtimestamp(deal.time),
                    'comment': deal.comment
                })

            return result

        except Exception as e:
            self.logger.error(f"Error getting trade history: {e}")
            return []

    def _get_order_type_string(self, order_type: int) -> str:
        """Convert MT4 order type to string"""
        type_map = {
            mt4.ORDER_BUY: 'BUY',
            mt4.ORDER_SELL: 'SELL',
            mt4.ORDER_BUY_LIMIT: 'BUY_LIMIT',
            mt4.ORDER_SELL_LIMIT: 'SELL_LIMIT',
            mt4.ORDER_BUY_STOP: 'BUY_STOP',
            mt4.ORDER_SELL_STOP: 'SELL_STOP'
        }
        return type_map.get(order_type, 'UNKNOWN')

    def shutdown(self):
        """Shutdown MT4 connection"""
        self.disconnect()