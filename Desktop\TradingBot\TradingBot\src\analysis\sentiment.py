# src/analysis/sentiment.py
import requests
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
from textblob import TextBlob
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer

# Download NLTK data (would typically be done once during setup)
nltk.download('vader_lexicon', quiet=True)

class NewsSentimentAnalyzer:
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('news', {})
        self.api_key = self.config.get('api_key')
        self.sources = self.config.get('sources', ['Reuters', 'Bloomberg'])
        self.sia = SentimentIntensityAnalyzer()
        self.cache = {}
        
    def get_sentiment(self, symbol: str, lookback_hours: int = 24) -> Dict[str, Any]:
        """
        Get news sentiment for a given symbol
        Returns dictionary with sentiment scores and analysis
        """
        # Check cache first
        cache_key = f"{symbol}_{lookback_hours}"
        if cache_key in self.cache:
            if datetime.now() - self.cache_expiry[cache_key] < timedelta(minutes=15):
                return self.cache[cache_key]
        
        # Get news articles
        articles = self._fetch_news_articles(symbol, lookback_hours)
        if not articles:
            return {
                'score': 0.5,
                'sentiment': 'neutral',
                'articles_analyzed': 0,
                'error': 'No articles found'
            }
        
        # Analyze sentiment
        sentiment_results = []
        for article in articles:
            analysis = self._analyze_article(article)
            sentiment_results.append(analysis)
        
        # Calculate aggregate scores
        aggregate = self._aggregate_sentiment(sentiment_results)
        
        # Cache results
        self.cache[cache_key] = aggregate
        self.cache_expiry[cache_key] = datetime.now()
        
        return aggregate
    
    def _fetch_news_articles(self, symbol: str, lookback_hours: int) -> Optional[list]:
        """Fetch news articles from API"""
        if not self.api_key:
            return None
            
        time_from = (datetime.now() - timedelta(hours=lookback_hours)).strftime('%Y-%m-%dT%H:%M:%S')
        
        try:
            params = {
                'q': symbol,
                'from': time_from,
                'sortBy': 'publishedAt',
                'apiKey': self.api_key,
                'sources': ','.join(self.sources),
                'language': 'en'
            }
            
            response = requests.get(
                'https://newsapi.org/v2/everything',
                params=params,
                timeout=10
            )
            response.raise_for_status()
            
            data = response.json()
            return data.get('articles', [])
        except Exception as e:
            print(f"Error fetching news: {str(e)}")
            return None
    
    def _analyze_article(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment of a single news article"""
        title = article.get('title', '')
        description = article.get('description', '')
        content = article.get('content', '')
        
        # Combine text for analysis (title is most important)
        text = f"{title}. {description}. {content}"
        
        # TextBlob sentiment
        blob = TextBlob(text)
        tb_polarity = blob.sentiment.polarity  # -1 to 1
        tb_subjectivity = blob.sentiment.subjectivity  # 0 to 1
        
        # VADER sentiment
        vader_scores = self.sia.polarity_scores(text)
        
        # Custom scoring (you can adjust weights)
        score = (
            0.4 * tb_polarity + 
            0.6 * vader_scores['compound'] - 
            0.1 * tb_subjectivity  # Less weight on subjective content
        )
        
        # Normalize to 0-1 range
        normalized_score = (score + 1) / 2
        
        return {
            'title': title,
            'source': article.get('source', {}).get('name'),
            'published_at': article.get('publishedAt'),
            'textblob': {
                'polarity': tb_polarity,
                'subjectivity': tb_subjectivity
            },
            'vader': vader_scores,
            'composite_score': normalized_score,
            'sentiment': 'positive' if normalized_score > 0.6 else 
                        'negative' if normalized_score < 0.4 else 'neutral'
        }
    
    def _aggregate_sentiment(self, sentiment_results: list) -> Dict[str, Any]:
        """Aggregate sentiment across multiple articles"""
        if not sentiment_results:
            return {
                'score': 0.5,
                'sentiment': 'neutral',
                'articles_analyzed': 0
            }
        
        total = len(sentiment_results)
        avg_score = sum(r['composite_score'] for r in sentiment_results) / total
        positive = sum(1 for r in sentiment_results if r['sentiment'] == 'positive')
        negative = sum(1 for r in sentiment_results if r['sentiment'] == 'negative')
        
        # Weighted score by article source reliability
        source_weights = {
            'Reuters': 1.2,
            'Bloomberg': 1.1,
            'CNBC': 1.0,
            'Wall Street Journal': 1.15,
            'default': 0.9
        }
        
        weighted_scores = []
        for r in sentiment_results:
            source = r['source']
            weight = source_weights.get(source, source_weights['default'])
            weighted_scores.append(r['composite_score'] * weight)
        
        weighted_avg = sum(weighted_scores) / len(weighted_scores) if weighted_scores else avg_score
        
        return {
            'score': weighted_avg,
            'simple_average': avg_score,
            'sentiment': 'positive' if weighted_avg > 0.6 else 
                        'negative' if weighted_avg < 0.4 else 'neutral',
            'articles_analyzed': total,
            'positive_articles': positive,
            'negative_articles': negative,
            'neutral_articles': total - positive - negative,
            'articles': sentiment_results[:5]  # Return first 5 articles for review
        }