//+------------------------------------------------------------------+
//|                                        UltimateBotConnector.mq4 |
//|                                Ultimate Trading Bot Connector   |
//+------------------------------------------------------------------+
#property copyright "Ultimate Trading Bot"
#property version   "1.00"
#property strict

string commands_file = "python_commands.txt";
string responses_file = "python_responses.txt";
string status_file = "mt4_status.txt";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("Ultimate Bot Connector Started");
    
    // Create status file
    int file_handle = FileOpen(status_file, FILE_WRITE|FILE_TXT);
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "MT4_READY_" + TimeToString(TimeCurrent()));
        FileClose(file_handle);
        Print("Status file created successfully");
    }
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    CheckForCommands();
}

//+------------------------------------------------------------------+
//| Check for Python commands                                       |
//+------------------------------------------------------------------+
void CheckForCommands()
{
    if(!FileIsExist(commands_file))
        return;
        
    int file_handle = FileOpen(commands_file, FILE_READ|FILE_TXT);
    if(file_handle == INVALID_HANDLE)
        return;
        
    string command_json = "";
    while(!FileIsEnding(file_handle))
    {
        command_json += FileReadString(file_handle);
    }
    FileClose(file_handle);
    
    // Delete command file
    FileDelete(commands_file);
    
    if(StringLen(command_json) > 0)
    {
        ProcessCommand(command_json);
    }
}

//+------------------------------------------------------------------+
//| Process command from Python                                     |
//+------------------------------------------------------------------+
void ProcessCommand(string command_json)
{
    Print("Processing command: ", command_json);
    
    string command = GetJsonValue(command_json, "command");
    string id = GetJsonValue(command_json, "id");
    
    string response = "";
    
    if(command == "ping")
    {
        response = "{\"success\":true,\"data\":\"pong\",\"id\":\"" + id + "\"}";
    }
    else if(command == "account_info")
    {
        response = "{\"success\":true,\"data\":{";
        response += "\"balance\":" + DoubleToString(AccountBalance(), 2) + ",";
        response += "\"equity\":" + DoubleToString(AccountEquity(), 2) + ",";
        response += "\"account_number\":\"" + IntegerToString(AccountNumber()) + "\",";
        response += "\"server\":\"" + AccountServer() + "\"";
        response += "},\"id\":\"" + id + "\"}";
    }
    else if(command == "get_price")
    {
        string symbol = GetJsonValue(command_json, "symbol");
        double bid = MarketInfo(symbol, MODE_BID);
        double ask = MarketInfo(symbol, MODE_ASK);
        
        response = "{\"success\":true,\"data\":{";
        response += "\"symbol\":\"" + symbol + "\",";
        response += "\"bid\":" + DoubleToString(bid, 5) + ",";
        response += "\"ask\":" + DoubleToString(ask, 5);
        response += "},\"id\":\"" + id + "\"}";
    }
    else if(command == "place_order")
    {
        string symbol = GetJsonValue(command_json, "symbol");
        string order_type = GetJsonValue(command_json, "order_type");
        double volume = StringToDouble(GetJsonValue(command_json, "volume"));
        double price = StringToDouble(GetJsonValue(command_json, "price"));
        double sl = StringToDouble(GetJsonValue(command_json, "sl"));
        double tp = StringToDouble(GetJsonValue(command_json, "tp"));
        
        int cmd = (order_type == "buy") ? OP_BUY : OP_SELL;
        if(order_type == "buy") price = MarketInfo(symbol, MODE_ASK);
        if(order_type == "sell") price = MarketInfo(symbol, MODE_BID);
        
        int ticket = OrderSend(symbol, cmd, volume, price, 3, sl, tp, "Ultimate Bot", 0, 0, clrNONE);
        
        if(ticket > 0)
        {
            response = "{\"success\":true,\"data\":{\"ticket\":" + IntegerToString(ticket) + "},\"id\":\"" + id + "\"}";
        }
        else
        {
            response = "{\"success\":false,\"error\":\"Order failed: " + IntegerToString(GetLastError()) + "\",\"id\":\"" + id + "\"}";
        }
    }
    else
    {
        response = "{\"success\":false,\"error\":\"Unknown command\",\"id\":\"" + id + "\"}";
    }
    
    // Write response
    int file_handle = FileOpen(responses_file, FILE_WRITE|FILE_TXT);
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, response);
        FileClose(file_handle);
        Print("Response sent: ", response);
    }
}

//+------------------------------------------------------------------+
//| Simple JSON value extractor                                     |
//+------------------------------------------------------------------+
string GetJsonValue(string json, string key)
{
    // Try multiple search patterns
    string search1 = "\"" + key + "\":\"";
    string search2 = "\"" + key + "\": \"";
    string search3 = "\"" + key + "\":";

    int start = StringFind(json, search1);
    int search_len = StringLen(search1);

    if(start == -1)
    {
        start = StringFind(json, search2);
        search_len = StringLen(search2);
    }

    if(start == -1)
    {
        start = StringFind(json, search3);
        search_len = StringLen(search3);
    }

    if(start == -1) return "";

    start += search_len;

    // Skip whitespace and quotes
    while(start < StringLen(json) &&
          (StringGetChar(json, start) == ' ' || StringGetChar(json, start) == '"'))
        start++;

    int end = start;

    // Find end of value
    while(end < StringLen(json))
    {
        int ch = StringGetChar(json, end);
        if(ch == '"' || ch == ',' || ch == '}' || ch == ' ')
            break;
        end++;
    }

    string result = StringSubstr(json, start, end - start);

    // Clean up result
    while(StringLen(result) > 0 &&
          (StringGetChar(result, StringLen(result)-1) == '"' ||
           StringGetChar(result, StringLen(result)-1) == ' '))
    {
        result = StringSubstr(result, 0, StringLen(result)-1);
    }

    return result;
}
