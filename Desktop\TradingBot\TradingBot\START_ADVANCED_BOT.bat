@echo off
chcp 65001 >nul
echo ================================================
echo ADVANCED TRADING BOT v2.0
echo ================================================
echo.
echo Enhanced Features:
echo    * Multi-indicator analysis (SMA, EMA, RSI, MACD, Bollinger Bands)
echo    * Automatic stop-loss and take-profit
echo    * Advanced risk management (1.5%% risk per trade)
echo    * Position sizing based on account balance
echo    * Daily loss limits and trade frequency controls
echo    * Real-time position monitoring
echo.
echo Account: ******** (FBS-Demo)
echo Risk per trade: 1.5%%
echo Risk-reward ratio: 1:2
echo Minimum confidence: 60%%
echo.
echo Press Ctrl+C to stop the bot
echo ================================================
echo.

REM Change to the correct directory
cd /d "%~dp0"

REM Check current directory
echo Current directory: %CD%
echo.

REM Run the advanced trading bot
env\Scripts\python.exe advanced_trading_bot.py

echo.
echo ================================================
echo Advanced trading bot stopped
echo ================================================
pause
