@echo off
echo ================================================
echo 🤖 STARTING AUTOMATED TRADING BOT
echo ================================================
echo.
echo Account: ******** (FBS-Demo)
echo Strategy: SMA Crossover
echo Risk: 2%% per trade
echo.
echo Press Ctrl+C to stop the bot
echo ================================================
echo.

REM Change to the correct directory
cd /d "%~dp0"

REM Check if we're in the right place
echo Current directory: %CD%
echo.

REM Run the trading bot (updated path)
env\Scripts\python.exe simple_trading_bot.py

echo.
echo ================================================
echo Trading bot stopped
echo ================================================
pause
