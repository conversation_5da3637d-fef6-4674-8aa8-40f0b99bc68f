# src/data/historical.py
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from ..brokers import IBroker

class HistoricalDataManager:
    def __init__(self, broker: IBroker, data_dir: str = "data/historical"):
        self.broker = broker
        self.data_dir = data_dir
        os.makedirs(self.data_dir, exist_ok=True)
        
    def get_data(
        self,
        symbol: str,
        timeframe: str,
        start: Optional[datetime] = None,
        end: Optional[datetime] = None,
        count: Optional[int] = None,
        cache: bool = True
    ) -> pd.DataFrame:
        """Get historical data either from cache or broker"""
        # Try to load from cache first
        if cache:
            cached = self._load_from_cache(symbol, timeframe, start, end, count)
            if cached is not None:
                return cached
                
        # Fetch from broker if not in cache
        data = self._fetch_from_broker(symbol, timeframe, start, end, count)
        
        # Save to cache
        if cache and data is not None:
            self._save_to_cache(symbol, timeframe, data)
            
        return data
        
    def _fetch_from_broker(
        self,
        symbol: str,
        timeframe: str,
        start: Optional[datetime],
        end: Optional[datetime],
        count: Optional[int]
    ) -> pd.DataFrame:
        """Fetch historical data from broker"""
        rates = self.broker.get_historical_data(
            symbol=symbol,
            timeframe=timeframe,
            start=start,
            end=end,
            count=count
        )
        
        if not rates:
            return None
            
        df = pd.DataFrame(rates)
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        return df
        
    def _load_from_cache(
        self,
        symbol: str,
        timeframe: str,
        start: Optional[datetime],
        end: Optional[datetime],
        count: Optional[int]
    ) -> Optional[pd.DataFrame]:
        """Load historical data from local cache"""
        cache_file = self._get_cache_file(symbol, timeframe)
        if not os.path.exists(cache_file):
            return None
            
        try:
            df = pd.read_parquet(cache_file)
            
            # Filter by date range if specified
            if start is not None:
                df = df[df.index >= start]
            if end is not None:
                df = df[df.index <= end]
                
            # Limit by count if specified
            if count is not None:
                df = df.tail(count)
                
            return df
        except Exception:
            return None
            
    def _save_to_cache(self, symbol: str, timeframe: str, data: pd.DataFrame):
        """Save historical data to local cache"""
        cache_file = self._get_cache_file(symbol, timeframe)
        data.to_parquet(cache_file)
        
    def _get_cache_file(self, symbol: str, timeframe: str) -> str:
        """Get cache file path for symbol/timeframe"""
        safe_symbol = symbol.replace('/', '_')
        return os.path.join(self.data_dir, f"{safe_symbol}_{timeframe}.parquet")
        
    def update_cache(self, symbols: List[str], timeframes: List[str], days: int = 30):
        """Update cache for multiple symbols and timeframes"""
        end = datetime.now()
        start = end - timedelta(days=days)
        
        for symbol in symbols:
            for timeframe in timeframes:
                self.get_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    start=start,
                    end=end,
                    cache=True
                )