# src/data/realtime.py
import threading
import queue
import time
from datetime import datetime
from typing import Dict, Callable, List
from ..brokers import IBroker

class RealTimeDataFeed:
    def __init__(self, broker: IBroker):
        self.broker = broker
        self.subscribers = {}
        self.data_queues = {}
        self.running = False
        self.thread = None
        
    def subscribe(
        self,
        symbol: str,
        callback: Callable[[Dict], None] = None,
        return_queue: bool = False
    ) -> Optional[queue.Queue]:
        """Subscribe to real-time data for a symbol"""
        if symbol not in self.subscribers:
            self.subscribers[symbol] = []
            self.data_queues[symbol] = queue.Queue()
            
        if callback:
            self.subscribers[symbol].append(callback)
            
        if return_queue:
            return self.data_queues[symbol]
            
    def unsubscribe(self, symbol: str, callback: Callable[[Dict], None] = None):
        """Unsubscribe from real-time data"""
        if symbol in self.subscribers:
            if callback:
                if callback in self.subscribers[symbol]:
                    self.subscribers[symbol].remove(callback)
            else:
                del self.subscribers[symbol]
                del self.data_queues[symbol]
                
    def start(self, interval: float = 1.0):
        """Start the real-time data feed"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(
            target=self._run,
            args=(interval,),
            daemon=True
        )
        self.thread.start()
        
    def stop(self):
        """Stop the real-time data feed"""
        self.running = False
        if self.thread:
            self.thread.join()
            
    def _run(self, interval: float):
        """Main data feed loop"""
        while self.running:
            try:
                self._update_prices()
                time.sleep(interval)
            except Exception as e:
                print(f"Error in real-time data feed: {str(e)}")
                time.sleep(5)
                
    def _update_prices(self):
        """Update prices for all subscribed symbols"""
        if not self.subscribers:
            return
            
        symbols = list(self.subscribers.keys())
        prices = {}
        
        # Batch get prices for efficiency
        for symbol in symbols:
            prices[symbol] = self.broker.get_current_price(symbol)
            
        timestamp = datetime.now()
        
        # Notify subscribers
        for symbol, price_data in prices.items():
            data = {
                'symbol': symbol,
                'timestamp': timestamp,
                'bid': price_data['bid'],
                'ask': price_data['ask'],
                'last': (price_data['bid'] + price_data['ask']) / 2
            }
            
            # Call callbacks
            for callback in self.subscribers.get(symbol, []):
                try:
                    callback(data)
                except Exception as e:
                    print(f"Error in data callback: {str(e)}")
                    
            # Put data in queue
            if symbol in self.data_queues:
                self.data_queues[symbol].put(data)