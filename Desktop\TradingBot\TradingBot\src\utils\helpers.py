# src/utils/helpers.py
import math
from typing import Union, Dict
from datetime import datetime, timedelta

def format_price(price: float, digits: int = 5) -> str:
    """Format price with specified decimal places"""
    return f"{price:.{digits}f}"
    
def calculate_pips(price1: float, price2: float, symbol: str) -> float:
    """Calculate pips difference between two prices"""
    if 'JPY' in symbol:
        multiplier = 100
    else:
        multiplier = 10000
    return abs(price1 - price2) * multiplier
    
def percentage_change(old: float, new: float) -> float:
    """Calculate percentage change between two values"""
    if old == 0:
        return 0
    return ((new - old) / old) * 100
    
def round_down(value: float, decimals: int = 2) -> float:
    """Round down to specified decimal places"""
    factor = 10 ** decimals
    return math.floor(value * factor) / factor
    
def is_market_open(symbol: str, current_time: datetime = None) -> bool:
    """Check if market is open for trading"""
    if current_time is None:
        current_time = datetime.now()
        
    # Define market hours (simplified)
    market_hours = {
        'FOREX': (datetime.strptime('00:00', '%H:%M'), datetime.strptime('23:59', '%H:%M')),
        'XAUUSD': (datetime.strptime('00:00', '%H:%M'), datetime.strptime('23:59', '%H:%M')),
        'NAS100': (datetime.strptime('14:30', '%H:%M'), datetime.strptime('21:00', '%H:%M')),
        'US30': (datetime.strptime('14:30', '%H:%M'), datetime.strptime('21:00', '%H:%M'))
    }
    
    market_type = 'FOREX'
    if 'XAU' in symbol:
        market_type = 'XAUUSD'
    elif 'NAS' in symbol:
        market_type = 'NAS100'
    elif 'US30' in symbol:
        market_type = 'US30'
        
    open_time, close_time = market_hours[market_type]
    current_time = current_time.time()
    
    return open_time.time() <= current_time <= close_time.time()
